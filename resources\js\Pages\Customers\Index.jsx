import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Users, Plus, Search, Filter, Mail, Phone, Building } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, customers = { data: [], links: [] } }) {
    // Mock data for now
    const mockCustomers = customers.data || [
        {
            id: 1,
            name: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            company: "Johnson Events",
            address: "123 Main St, Downtown, NY 10001",
            notes: "Prefers vegetarian options",
            created_at: "2024-01-15T10:30:00Z",
            orders_count: 5,
            total_spent: 12500.00,
            last_order_date: "2024-01-20"
        },
        {
            id: 2,
            name: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            company: "TechCorp Inc",
            address: "456 Business Ave, Corporate District, NY 10002",
            notes: "Corporate events, requires invoicing",
            created_at: "2024-01-10T14:20:00Z",
            orders_count: 8,
            total_spent: 18750.00,
            last_order_date: "2024-01-18"
        },
        {
            id: 3,
            name: "Emily Rodriguez",
            email: "<EMAIL>",
            phone: "+****************",
            company: null,
            address: "789 Residential Blvd, Suburbs, NY 10003",
            notes: "Birthday parties and family events",
            created_at: "2024-01-05T09:15:00Z",
            orders_count: 3,
            total_spent: 4200.00,
            last_order_date: "2024-01-12"
        },
        {
            id: 4,
            name: "David Thompson",
            email: "<EMAIL>",
            phone: "+****************",
            company: "Elite Wedding Planning",
            address: "321 Wedding Way, Event District, NY 10004",
            notes: "Wedding specialist, high-end events",
            created_at: "2023-12-20T16:45:00Z",
            orders_count: 12,
            total_spent: 35000.00,
            last_order_date: "2024-01-15"
        }
    ];

    const getCustomerType = (customer) => {
        if (customer.company) {
            return { type: 'Business', color: 'bg-blue-100 text-blue-800' };
        }
        return { type: 'Individual', color: 'bg-green-100 text-green-800' };
    };

    const totalCustomers = mockCustomers.length;
    const businessCustomers = mockCustomers.filter(c => c.company).length;
    const totalRevenue = mockCustomers.reduce((sum, customer) => sum + customer.total_spent, 0);

    return (
        <CaterProLayout>
            <Head title="Customers" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Users className="h-6 w-6 mr-3 text-primary" />
                            Customers
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your customer relationships and contact information</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('customers.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Customer
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                                    <p className="text-2xl font-bold">{totalCustomers}</p>
                                </div>
                                <Users className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Business Clients</p>
                                    <p className="text-2xl font-bold">{businessCustomers}</p>
                                </div>
                                <Building className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                                    <p className="text-2xl font-bold">${totalRevenue.toFixed(2)}</p>
                                </div>
                                <Users className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Customers</CardTitle>
                        <CardDescription>Find customers by name, email, or company</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search customers..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Customers Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>All Customers ({mockCustomers.length})</CardTitle>
                        <CardDescription>Manage your customer database</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Customer</th>
                                        <th className="text-left p-3 font-medium">Contact</th>
                                        <th className="text-left p-3 font-medium">Type</th>
                                        <th className="text-left p-3 font-medium">Orders</th>
                                        <th className="text-left p-3 font-medium">Total Spent</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockCustomers.map((customer) => {
                                        const customerType = getCustomerType(customer);
                                        return (
                                            <tr key={customer.id} className="border-b hover:bg-muted/50">
                                                <td className="p-3">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                            <Users className="h-5 w-5 text-primary" />
                                                        </div>
                                                        <div>
                                                            <div className="font-medium">{customer.name}</div>
                                                            {customer.company && (
                                                                <div className="text-sm text-muted-foreground">{customer.company}</div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center text-sm">
                                                            <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                                                            {customer.email}
                                                        </div>
                                                        {customer.phone && (
                                                            <div className="flex items-center text-sm text-muted-foreground">
                                                                <Phone className="h-4 w-4 mr-2" />
                                                                {customer.phone}
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <Badge className={customerType.color}>
                                                        {customerType.type}
                                                    </Badge>
                                                </td>
                                                <td className="p-3">
                                                    <div>
                                                        <div className="font-medium">{customer.orders_count} orders</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            Last: {new Date(customer.last_order_date).toLocaleDateString()}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <span className="font-semibold">${customer.total_spent.toFixed(2)}</span>
                                                </td>
                                                <td className="p-3">
                                                    <div className="flex gap-2">
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('customers.show', customer.id)}>
                                                                View
                                                            </Link>
                                                        </Button>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('customers.edit', customer.id)}>
                                                                Edit
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
