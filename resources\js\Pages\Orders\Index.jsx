import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { ShoppingCart, Plus, Search, Filter, Calendar, Users, DollarSign } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, orders = { data: [], links: [] } }) {
    // Mock data for now
    const mockOrders = orders.data || [
        {
            id: 1,
            customer: { name: "<PERSON>", email: "<EMAIL>" },
            event_name: "Wedding Reception",
            event_date: "2024-02-15",
            event_address: "Grand Ballroom, Downtown Hotel",
            total_amount: 2500.00,
            status: "confirmed",
            notes: "Vegetarian options required"
        },
        {
            id: 2,
            customer: { name: "Corporate Events Inc", email: "<EMAIL>" },
            event_name: "Annual Company Meeting",
            event_date: "2024-02-10",
            event_address: "Conference Center, Business District",
            total_amount: 1800.00,
            status: "in_progress",
            notes: "Setup required by 8 AM"
        },
        {
            id: 3,
            customer: { name: "Mike Thompson", email: "<EMAIL>" },
            event_name: "Birthday Party",
            event_date: "2024-02-08",
            event_address: "Private Residence",
            total_amount: 650.00,
            status: "completed",
            notes: "Outdoor setup"
        },
        {
            id: 4,
            customer: { name: "Lisa Chen", email: "<EMAIL>" },
            event_name: "Graduation Celebration",
            event_date: "2024-02-20",
            event_address: "Community Center",
            total_amount: 950.00,
            status: "pending",
            notes: "Dietary restrictions to be confirmed"
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'in_progress':
                return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
            case 'completed':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'cancelled':
                return 'bg-red-100 text-red-800 hover:bg-red-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const totalRevenue = mockOrders.reduce((sum, order) => sum + order.total_amount, 0);
    const completedOrders = mockOrders.filter(order => order.status === 'completed').length;

    return (
        <CaterProLayout>
            <Head title="Orders" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <ShoppingCart className="h-6 w-6 mr-3 text-primary" />
                            Orders
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your catering orders and events</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('orders.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                New Order
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                                    <p className="text-2xl font-bold">{mockOrders.length}</p>
                                </div>
                                <ShoppingCart className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Completed</p>
                                    <p className="text-2xl font-bold">{completedOrders}</p>
                                </div>
                                <Users className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                                    <p className="text-2xl font-bold">${totalRevenue.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">This Month</p>
                                    <p className="text-2xl font-bold">{mockOrders.length}</p>
                                </div>
                                <Calendar className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Orders</CardTitle>
                        <CardDescription>Find orders by customer, event name, or status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search orders..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Orders Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Orders ({mockOrders.length})</CardTitle>
                        <CardDescription>All your catering orders</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Order</th>
                                        <th className="text-left p-3 font-medium">Customer</th>
                                        <th className="text-left p-3 font-medium">Event Date</th>
                                        <th className="text-left p-3 font-medium">Amount</th>
                                        <th className="text-left p-3 font-medium">Status</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockOrders.map((order) => (
                                        <tr key={order.id} className="border-b hover:bg-muted/50">
                                            <td className="p-3">
                                                <div className="flex items-center">
                                                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                        <ShoppingCart className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">#{order.id} - {order.event_name}</div>
                                                        <div className="text-sm text-muted-foreground line-clamp-1">
                                                            {order.event_address}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <div>
                                                    <div className="font-medium">{order.customer.name}</div>
                                                    <div className="text-sm text-muted-foreground">{order.customer.email}</div>
                                                </div>
                                            </td>
                                            <td className="p-3 text-muted-foreground">
                                                {new Date(order.event_date).toLocaleDateString()}
                                            </td>
                                            <td className="p-3">
                                                <span className="font-semibold">${order.total_amount.toFixed(2)}</span>
                                            </td>
                                            <td className="p-3">
                                                <Badge className={getStatusBadge(order.status)}>
                                                    {order.status.replace('_', ' ')}
                                                </Badge>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('orders.show', order.id)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('orders.edit', order.id)}>
                                                            Edit
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
