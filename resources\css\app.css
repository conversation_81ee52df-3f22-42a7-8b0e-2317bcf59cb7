@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* CaterPro Brand Colors - Modern Restaurant Theme */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary: Warm Orange (Restaurant/Food Industry) */
    --primary: 24 95% 53%;
    --primary-foreground: 0 0% 98%;

    /* Secondary: Elegant Gray */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    /* Muted: Soft Gray */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    /* Accent: Warm Gold */
    --accent: 45 93% 47%;
    --accent-foreground: 240 5.9% 10%;

    /* Success: Fresh Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    /* Warning: Amber */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    /* Destructive: Modern Red */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Borders and Inputs */
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 24 95% 53%;

    /* Chart Colors - Food Industry Themed */
    --chart-1: 24 95% 53%;  /* Primary Orange */
    --chart-2: 142 76% 36%; /* Success Green */
    --chart-3: 45 93% 47%;  /* Accent Gold */
    --chart-4: 262 83% 58%; /* Purple */
    --chart-5: 346 87% 43%; /* Rose */

    --radius: 0.75rem;

    /* Sidebar Colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 24 95% 53%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 24 95% 53%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary: Bright Orange for dark mode */
    --primary: 24 95% 53%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 24 95% 53%;

    /* Dark mode chart colors */
    --chart-1: 24 95% 53%;  /* Primary Orange */
    --chart-2: 142 76% 36%; /* Success Green */
    --chart-3: 45 93% 47%;  /* Accent Gold */
    --chart-4: 262 83% 58%; /* Purple */
    --chart-5: 346 87% 43%; /* Rose */

    /* Dark sidebar */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 24 95% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 24 95% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* CaterPro Custom Components */
  .caterpro-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm;
  }

  .caterpro-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 font-medium transition-colors;
  }

  .caterpro-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 rounded-md px-4 py-2 font-medium transition-colors;
  }

  .caterpro-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .caterpro-table {
    @apply w-full caption-bottom text-sm;
  }

  .caterpro-table-header {
    @apply border-b bg-muted/50 font-medium text-muted-foreground;
  }

  .caterpro-table-row {
    @apply border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
  }

  .caterpro-badge-success {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-100 text-green-800 border-green-200;
  }

  .caterpro-badge-warning {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-yellow-100 text-yellow-800 border-yellow-200;
  }

  .caterpro-badge-danger {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-red-100 text-red-800 border-red-200;
  }

  .caterpro-sidebar {
    @apply flex h-full w-64 flex-col border-r bg-sidebar-background text-sidebar-foreground;
  }

  .caterpro-main-content {
    @apply flex-1 overflow-auto p-6;
  }

  .caterpro-stats-card {
    @apply caterpro-card p-6 space-y-2;
  }

  .caterpro-form-section {
    @apply space-y-4 p-6 border rounded-lg bg-card;
  }

  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .stagger-1 {
    animation-delay: 0.1s;
  }

  .stagger-2 {
    animation-delay: 0.2s;
  }

  .stagger-3 {
    animation-delay: 0.3s;
  }

  .stagger-4 {
    animation-delay: 0.4s;
  }

  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .pulse-subtle {
    animation: pulseSubtle 2s ease-in-out infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
