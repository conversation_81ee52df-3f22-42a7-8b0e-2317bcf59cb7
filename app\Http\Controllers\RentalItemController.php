<?php

namespace App\Http\Controllers;

use App\Models\RentalItem;
use Illuminate\Http\Request;
use Inertia\Inertia;

class RentalItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rentalItems = RentalItem::latest()->paginate(10);

        return Inertia::render('Rentals/Index', [
            'rentalItems' => $rentalItems,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Rentals/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:rental_items',
            'description' => 'nullable|string',
            'quantity' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'status' => 'required|string|in:available,rented,maintenance',
        ]);

        RentalItem::create($validated);

        return redirect(route('rentals.index'))->with('success', 'Rental item created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(RentalItem $rentalItem)
    {
        return Inertia::render('Rentals/Show', [
            'rentalItem' => $rentalItem,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RentalItem $rentalItem)
    {
        return Inertia::render('Rentals/Edit', [
            'rentalItem' => $rentalItem,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RentalItem $rentalItem)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:rental_items,sku,' . $rentalItem->id,
            'description' => 'nullable|string',
            'quantity' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'status' => 'required|string|in:available,rented,maintenance',
        ]);

        $rentalItem->update($validated);

        return redirect(route('rentals.index'))->with('success', 'Rental item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RentalItem $rentalItem)
    {
        $rentalItem->delete();

        return redirect(route('rentals.index'))->with('success', 'Rental item deleted successfully.');
    }
}
