import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { DollarSign, ArrowLeft, Save, Receipt, Calendar, Tag } from 'lucide-react';

export default function Create({ auth }) {
    const { data, setData, post, processing, errors } = useForm({
        title: '',
        description: '',
        amount: '',
        category: '',
        expense_date: new Date().toISOString().split('T')[0],
        receipt_number: '',
    });

    const submit = (e) => {
        e.preventDefault();
        post(route('expenses.store'));
    };

    const categories = [
        'Food & Ingredients',
        'Equipment',
        'Transportation',
        'Marketing',
        'Utilities',
        'Staff',
        'Insurance',
        'Rent',
        'Other'
    ];

    return (
        <CaterProLayout>
            <Head title="Add Expense" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <DollarSign className="h-6 w-6 mr-3 text-primary" />
                            Add Expense
                        </h1>
                        <p className="text-muted-foreground mt-2">Record a new business expense</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('expenses.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Expenses
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Expense Information</CardTitle>
                            <CardDescription>Enter the details for this expense</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="title">Expense Title *</Label>
                                    <Input
                                        id="title"
                                        type="text"
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="e.g., Food Supplies - Wholesale Market"
                                        required
                                    />
                                    <InputError message={errors.title} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={data.category} onValueChange={(value) => setData('category', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {categories.map((category) => (
                                                <SelectItem key={category} value={category}>
                                                    {category}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.category} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Describe the expense..."
                                    rows={3}
                                />
                                <InputError message={errors.description} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Amount & Details</CardTitle>
                            <CardDescription>Set the expense amount and additional details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="amount">Amount *</Label>
                                    <div className="relative">
                                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="amount"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            value={data.amount}
                                            onChange={(e) => setData('amount', e.target.value)}
                                            placeholder="0.00"
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.amount} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="expense_date">Expense Date *</Label>
                                    <div className="relative">
                                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="expense_date"
                                            type="date"
                                            value={data.expense_date}
                                            onChange={(e) => setData('expense_date', e.target.value)}
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.expense_date} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="receipt_number">Receipt Number</Label>
                                    <div className="relative">
                                        <Receipt className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="receipt_number"
                                            type="text"
                                            value={data.receipt_number}
                                            onChange={(e) => setData('receipt_number', e.target.value)}
                                            placeholder="e.g., RCP-001-2024"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.receipt_number} />
                                </div>
                            </div>

                            {data.amount && (
                                <div className="bg-muted/50 p-4 rounded-lg">
                                    <h4 className="font-medium mb-2">Expense Summary</h4>
                                    <div className="flex justify-between items-center">
                                        <span>Total Amount:</span>
                                        <span className="text-lg font-semibold text-primary">
                                            ${parseFloat(data.amount || 0).toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('expenses.index')}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Creating...' : 'Create Expense'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
