import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { ShoppingCart, ArrowLeft, Save, DollarSign, Calendar, MapPin, Users } from 'lucide-react';

export default function Create({ auth, customers = [] }) {
    const { data, setData, post, processing, errors } = useForm({
        customer_id: '',
        event_name: '',
        event_date: '',
        event_address: '',
        total_amount: '',
        status: 'pending',
        notes: '',
    });

    const submit = (e) => {
        e.preventDefault();
        post(route('orders.store'));
    };

    // Mock customers if not provided
    const mockCustomers = customers.length > 0 ? customers : [
        { id: 1, name: "Sarah Johnson", email: "<EMAIL>" },
        { id: 2, name: "TechCorp Inc", email: "<EMAIL>" },
        { id: 3, name: "Mike Thompson", email: "<EMAIL>" },
        { id: 4, name: "Lisa Chen", email: "<EMAIL>" }
    ];

    return (
        <CaterProLayout>
            <Head title="Create Order" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <ShoppingCart className="h-6 w-6 mr-3 text-primary" />
                            Create New Order
                        </h1>
                        <p className="text-muted-foreground mt-2">Create a new catering order for your customer</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('orders.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Orders
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Customer & Event Information</CardTitle>
                            <CardDescription>Select customer and enter event details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="customer_id">Customer *</Label>
                                    <Select value={data.customer_id} onValueChange={(value) => setData('customer_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select customer" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {mockCustomers.map((customer) => (
                                                <SelectItem key={customer.id} value={customer.id.toString()}>
                                                    {customer.name} ({customer.email})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.customer_id} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="event_name">Event Name *</Label>
                                    <Input
                                        id="event_name"
                                        type="text"
                                        value={data.event_name}
                                        onChange={(e) => setData('event_name', e.target.value)}
                                        placeholder="e.g., Wedding Reception"
                                        required
                                    />
                                    <InputError message={errors.event_name} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="event_date">Event Date *</Label>
                                    <div className="relative">
                                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="event_date"
                                            type="datetime-local"
                                            value={data.event_date}
                                            onChange={(e) => setData('event_date', e.target.value)}
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.event_date} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status *</Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="confirmed">Confirmed</SelectItem>
                                            <SelectItem value="in_progress">In Progress</SelectItem>
                                            <SelectItem value="completed">Completed</SelectItem>
                                            <SelectItem value="cancelled">Cancelled</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.status} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="event_address">Event Address *</Label>
                                <div className="relative">
                                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                    <Textarea
                                        id="event_address"
                                        value={data.event_address}
                                        onChange={(e) => setData('event_address', e.target.value)}
                                        placeholder="Enter the full event address..."
                                        className="pl-10"
                                        rows={3}
                                        required
                                    />
                                </div>
                                <InputError message={errors.event_address} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Pricing & Additional Information</CardTitle>
                            <CardDescription>Set the total amount and any special notes</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="total_amount">Total Amount *</Label>
                                    <div className="relative">
                                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="total_amount"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            value={data.total_amount}
                                            onChange={(e) => setData('total_amount', e.target.value)}
                                            placeholder="0.00"
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.total_amount} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Special Notes</Label>
                                <Textarea
                                    id="notes"
                                    value={data.notes}
                                    onChange={(e) => setData('notes', e.target.value)}
                                    placeholder="Any special requirements, dietary restrictions, or additional notes..."
                                    rows={4}
                                />
                                <InputError message={errors.notes} />
                            </div>

                            {data.total_amount && (
                                <div className="bg-muted/50 p-4 rounded-lg">
                                    <h4 className="font-medium mb-2">Order Summary</h4>
                                    <div className="flex justify-between items-center">
                                        <span>Total Amount:</span>
                                        <span className="text-lg font-semibold text-primary">
                                            ${parseFloat(data.total_amount || 0).toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('orders.index')}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Creating...' : 'Create Order'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
