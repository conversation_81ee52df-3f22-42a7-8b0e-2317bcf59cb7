import { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { ThemeToggle } from '@/Components/ThemeToggle';
import {
  ChefHat,
  Package,
  ShoppingCart,
  Users,
  Settings,
  Menu,
  Home,
  Truck,
  DollarSign,
  Wrench,
  BarChart3,
  Building2
} from 'lucide-react';
import { Button } from '@/Components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/Components/ui/sheet';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';

const navigation = [
  { name: 'Dashboard', href: 'dashboard', icon: Home, current: false },
  { name: 'Inventory', href: 'inventory.index', icon: Package, current: false },
  { name: 'Orders', href: 'orders.index', icon: ShoppingCart, current: false },
  { name: 'Rentals', href: 'rentals.index', icon: Truck, current: false },
  { name: 'Services', href: 'services.index', icon: Wrench, current: false },
  { name: 'Customers', href: 'customers.index', icon: Users, current: false },
  { name: 'Suppliers', href: 'suppliers.index', icon: Building2, current: false },
  { name: 'Expenses', href: 'expenses.index', icon: DollarSign, current: false },
  { name: 'Reports', href: 'reports.index', icon: BarChart3, current: false },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

function SidebarContent({ user, currentRoute }) {
  return (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center px-6 border-b">
        <Link href="/" className="flex items-center space-x-2">
          <ChefHat className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold text-foreground">CaterPro</span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = currentRoute === item.href || currentRoute.startsWith(item.href);
          return (
            <Link
              key={item.name}
              href={route(item.href)}
              className={classNames(
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                'group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors'
              )}
            >
              <item.icon
                className={classNames(
                  isActive ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-accent-foreground',
                  'mr-3 h-5 w-5 flex-shrink-0'
                )}
                aria-hidden="true"
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      <Separator />

      {/* User section */}
      <div className="p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start p-2">
              <Avatar className="h-8 w-8 mr-3">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1 text-left">
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.email}</p>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={route('profile.edit')}>
                <Settings className="mr-2 h-4 w-4" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={route('logout')} method="post" as="button" className="w-full">
                Log Out
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <ThemeToggle />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

export default function CaterProLayout({ children }) {
  const { auth } = usePage().props;
  const user = auth.user;
  const currentRoute = route().current();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:border-r lg:bg-card">
        <SidebarContent user={user} currentRoute={currentRoute} />
      </div>

      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent user={user} currentRoute={currentRoute} />
        </SheetContent>
      </Sheet>

      {/* Main content */}
      <div className="flex flex-1 flex-col lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b bg-card px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="lg:hidden">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Open sidebar</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-64 p-0">
              <SidebarContent user={user} currentRoute={currentRoute} />
            </SheetContent>
          </Sheet>

          {/* Header content - simplified */}
          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1 items-center">
              {/* Empty space for future breadcrumbs or global actions */}
            </div>

            {/* Quick actions */}
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications badge */}
              <Badge variant="secondary" className="hidden sm:inline-flex">
                Live
              </Badge>

              {/* User menu for desktop */}
              <div className="hidden lg:block">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar} alt={user.name} />
                        <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href={route('profile.edit')}>
                        <Settings className="mr-2 h-4 w-4" />
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href={route('logout')} method="post" as="button" className="w-full">
                        Log Out
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <ThemeToggle />
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 overflow-auto">
          <div className="caterpro-main-content">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
