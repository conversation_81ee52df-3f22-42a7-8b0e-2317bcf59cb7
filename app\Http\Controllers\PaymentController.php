<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Purchase;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $payments = Payment::with(['supplier', 'purchase'])
            ->latest()
            ->paginate(10);

        return Inertia::render('Payments/Index', [
            'payments' => $payments,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Payments/Create', [
            'suppliers' => Supplier::all(),
            'purchases' => Purchase::with('supplier')->where('status', '!=', 'paid')->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_id' => 'nullable|exists:purchases,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated) {
            // Create payment
            $payment = Payment::create($validated);

            // Update purchase if specified
            if ($validated['purchase_id']) {
                $purchase = Purchase::find($validated['purchase_id']);
                $purchase->increment('paid_amount', $validated['amount']);
                $purchase->updatePaymentStatus();
            }
        });

        return redirect(route('payments.index'))->with('success', 'Payment recorded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        $payment->load(['supplier', 'purchase']);

        return Inertia::render('Payments/Show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        return Inertia::render('Payments/Edit', [
            'payment' => $payment,
            'suppliers' => Supplier::all(),
            'purchases' => Purchase::with('supplier')->where('status', '!=', 'paid')->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_id' => 'nullable|exists:purchases,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $payment) {
            $oldAmount = $payment->amount;
            $oldPurchaseId = $payment->purchase_id;

            // Update payment
            $payment->update($validated);

            // Adjust purchase amounts
            if ($oldPurchaseId) {
                $oldPurchase = Purchase::find($oldPurchaseId);
                $oldPurchase->decrement('paid_amount', $oldAmount);
                $oldPurchase->updatePaymentStatus();
            }

            if ($validated['purchase_id']) {
                $newPurchase = Purchase::find($validated['purchase_id']);
                $newPurchase->increment('paid_amount', $validated['amount']);
                $newPurchase->updatePaymentStatus();
            }
        });

        return redirect(route('payments.index'))->with('success', 'Payment updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        DB::transaction(function () use ($payment) {
            // Adjust purchase if linked
            if ($payment->purchase_id) {
                $purchase = Purchase::find($payment->purchase_id);
                $purchase->decrement('paid_amount', $payment->amount);
                $purchase->updatePaymentStatus();
            }

            $payment->delete();
        });

        return redirect(route('payments.index'))->with('success', 'Payment deleted successfully.');
    }
}
