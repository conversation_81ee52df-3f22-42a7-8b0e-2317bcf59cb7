const Ziggy = {"url":"http:\/\/127.0.0.1:8000","port":8000,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"inventory.index":{"uri":"inventory","methods":["GET","HEAD"]},"inventory.create":{"uri":"inventory\/create","methods":["GET","HEAD"]},"inventory.store":{"uri":"inventory","methods":["POST"]},"inventory.show":{"uri":"inventory\/{inventory}","methods":["GET","HEAD"],"parameters":["inventory"]},"inventory.edit":{"uri":"inventory\/{inventory}\/edit","methods":["GET","HEAD"],"parameters":["inventory"]},"inventory.update":{"uri":"inventory\/{inventory}","methods":["PUT","PATCH"],"parameters":["inventory"]},"inventory.destroy":{"uri":"inventory\/{inventory}","methods":["DELETE"],"parameters":["inventory"]},"rentals.index":{"uri":"rentals","methods":["GET","HEAD"]},"rentals.create":{"uri":"rentals\/create","methods":["GET","HEAD"]},"rentals.store":{"uri":"rentals","methods":["POST"]},"rentals.show":{"uri":"rentals\/{rental}","methods":["GET","HEAD"],"parameters":["rental"]},"rentals.edit":{"uri":"rentals\/{rental}\/edit","methods":["GET","HEAD"],"parameters":["rental"]},"rentals.update":{"uri":"rentals\/{rental}","methods":["PUT","PATCH"],"parameters":["rental"]},"rentals.destroy":{"uri":"rentals\/{rental}","methods":["DELETE"],"parameters":["rental"]},"orders.index":{"uri":"orders","methods":["GET","HEAD"]},"orders.create":{"uri":"orders\/create","methods":["GET","HEAD"]},"orders.store":{"uri":"orders","methods":["POST"]},"orders.show":{"uri":"orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"orders.edit":{"uri":"orders\/{order}\/edit","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"orders.update":{"uri":"orders\/{order}","methods":["PUT","PATCH"],"parameters":["order"],"bindings":{"order":"id"}},"orders.destroy":{"uri":"orders\/{order}","methods":["DELETE"],"parameters":["order"],"bindings":{"order":"id"}},"customers.index":{"uri":"customers","methods":["GET","HEAD"]},"customers.create":{"uri":"customers\/create","methods":["GET","HEAD"]},"customers.store":{"uri":"customers","methods":["POST"]},"customers.show":{"uri":"customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.edit":{"uri":"customers\/{customer}\/edit","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.update":{"uri":"customers\/{customer}","methods":["PUT","PATCH"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.destroy":{"uri":"customers\/{customer}","methods":["DELETE"],"parameters":["customer"],"bindings":{"customer":"id"}},"expenses.index":{"uri":"expenses","methods":["GET","HEAD"]},"expenses.create":{"uri":"expenses\/create","methods":["GET","HEAD"]},"expenses.store":{"uri":"expenses","methods":["POST"]},"expenses.show":{"uri":"expenses\/{expense}","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.edit":{"uri":"expenses\/{expense}\/edit","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.update":{"uri":"expenses\/{expense}","methods":["PUT","PATCH"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.destroy":{"uri":"expenses\/{expense}","methods":["DELETE"],"parameters":["expense"],"bindings":{"expense":"id"}},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
