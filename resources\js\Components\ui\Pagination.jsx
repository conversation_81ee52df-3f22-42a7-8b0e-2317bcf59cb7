import { Link } from '@inertiajs/react';

export default function Pagination({ links = [] }) {
    if (!links || links.length === 0) {
        return null;
    }

    return (
        <div className="mt-6 flex justify-center">
            {links.map((link, key) => {
                // If link.url is null or undefined, render a span instead of Link
                if (!link.url) {
                    return (
                        <span
                            key={key}
                            className="px-4 py-2 mx-1 border rounded-md text-sm text-gray-400 cursor-not-allowed bg-gray-100"
                            dangerouslySetInnerHTML={{ __html: link.label }}
                        />
                    );
                }

                return (
                    <Link
                        key={key}
                        href={link.url}
                        className={
                            `px-4 py-2 mx-1 border rounded-md text-sm ` +
                            (link.active
                                ? `bg-primary text-primary-foreground border-primary`
                                : `bg-background text-foreground hover:bg-accent hover:text-accent-foreground border-border`)
                        }
                        dangerouslySetInnerHTML={{ __html: link.label }}
                    />
                );
            })}
        </div>
    );
}