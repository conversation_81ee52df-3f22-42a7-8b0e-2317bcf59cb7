import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { <PERSON><PERSON> } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { 
    Users, 
    ArrowLeft, 
    Edit, 
    Trash2, 
    Mail, 
    Phone, 
    MapPin, 
    Building, 
    Calendar,
    ShoppingCart,
    DollarSign
} from 'lucide-react';

export default function Show({ auth, customer }) {
    // Mock data if not provided
    const customerData = customer || {
        id: 1,
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Johnson Events",
        address: "123 Main St, Downtown, NY 10001",
        notes: "Prefers vegetarian options for all events. Usually orders for 50-150 guests. Very detail-oriented and appreciates early communication about menu changes.",
        created_at: "2024-01-15T10:30:00Z",
        updated_at: "2024-01-20T14:45:00Z"
    };

    // Mock order history
    const orderHistory = [
        {
            id: 1,
            event_name: "Annual Company Retreat",
            event_date: "2024-01-20",
            total_amount: 2500.00,
            status: "completed",
            guests: 150
        },
        {
            id: 2,
            event_name: "Board Meeting Lunch",
            event_date: "2024-01-15",
            total_amount: 850.00,
            status: "completed",
            guests: 25
        },
        {
            id: 3,
            event_name: "Holiday Party",
            event_date: "2023-12-20",
            total_amount: 3200.00,
            status: "completed",
            guests: 200
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'in_progress':
                return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
            case 'completed':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'cancelled':
                return 'bg-red-100 text-red-800 hover:bg-red-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const getCustomerType = () => {
        if (customerData.company) {
            return { type: 'Business Customer', color: 'bg-blue-100 text-blue-800' };
        }
        return { type: 'Individual Customer', color: 'bg-green-100 text-green-800' };
    };

    const totalOrders = orderHistory.length;
    const totalSpent = orderHistory.reduce((sum, order) => sum + order.total_amount, 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;

    return (
        <CaterProLayout>
            <Head title={`Customer: ${customerData.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Users className="h-6 w-6 mr-3 text-primary" />
                            {customerData.name}
                        </h1>
                        <p className="text-muted-foreground mt-2">Customer profile and order history</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('customers.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Customers
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('customers.edit', customerData.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Information */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Customer Information</CardTitle>
                                <CardDescription>Contact details and basic information</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Full Name</h3>
                                        <p className="text-lg font-semibold">{customerData.name}</p>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Customer Type</h3>
                                        <Badge className={getCustomerType().color}>
                                            {getCustomerType().type}
                                        </Badge>
                                    </div>
                                </div>

                                <Separator />

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Mail className="h-4 w-4 mr-1" />
                                            Email Address
                                        </h3>
                                        <p className="text-foreground">{customerData.email}</p>
                                    </div>
                                    {customerData.phone && (
                                        <div>
                                            <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                                <Phone className="h-4 w-4 mr-1" />
                                                Phone Number
                                            </h3>
                                            <p className="text-foreground">{customerData.phone}</p>
                                        </div>
                                    )}
                                </div>

                                {customerData.company && (
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Building className="h-4 w-4 mr-1" />
                                            Company/Organization
                                        </h3>
                                        <p className="text-foreground">{customerData.company}</p>
                                    </div>
                                )}

                                {customerData.address && (
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <MapPin className="h-4 w-4 mr-1" />
                                            Address
                                        </h3>
                                        <p className="text-foreground">{customerData.address}</p>
                                    </div>
                                )}

                                {customerData.notes && (
                                    <>
                                        <Separator />
                                        <div>
                                            <h3 className="font-medium text-sm text-muted-foreground mb-2">Notes & Preferences</h3>
                                            <p className="text-foreground leading-relaxed">{customerData.notes}</p>
                                        </div>
                                    </>
                                )}
                            </CardContent>
                        </Card>

                        {/* Order History */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Order History</CardTitle>
                                <CardDescription>Recent orders from this customer</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {orderHistory.length > 0 ? (
                                    <div className="space-y-4">
                                        {orderHistory.map((order) => (
                                            <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                                                <div className="flex items-center">
                                                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                        <ShoppingCart className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <p className="font-medium">{order.event_name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {new Date(order.event_date).toLocaleDateString()} • {order.guests} guests
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <div className="text-right">
                                                        <p className="font-semibold">${order.total_amount.toFixed(2)}</p>
                                                        <Badge className={getStatusBadge(order.status)}>
                                                            {order.status}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>No orders yet</p>
                                        <p className="text-sm">This customer hasn't placed any orders</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Customer Statistics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Total Orders</span>
                                    <span className="text-lg font-semibold">{totalOrders}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <DollarSign className="h-4 w-4 mr-1" />
                                        Total Spent
                                    </span>
                                    <span className="text-lg font-semibold">${totalSpent.toFixed(2)}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Average Order</span>
                                    <span className="text-lg font-semibold">${averageOrderValue.toFixed(2)}</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Record Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Customer Since
                                    </span>
                                    <p className="text-sm">{new Date(customerData.created_at).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </span>
                                    <p className="text-sm">{new Date(customerData.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full" asChild>
                                    <Link href={route('orders.create', { customer_id: customerData.id })}>
                                        <ShoppingCart className="h-4 w-4 mr-2" />
                                        Create New Order
                                    </Link>
                                </Button>
                                <Button variant="outline" className="w-full">
                                    <Mail className="h-4 w-4 mr-2" />
                                    Send Email
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </CaterProLayout>
    );
}
