{"hash": "ee581a3d", "configHash": "fa04376e", "lockfileHash": "d8f7e541", "browserHash": "bd445fa2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "4c9a1065", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a55303f2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1b348fe6", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "740c16bc", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "f10a24db", "needsInterop": false}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "f502435d", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "acd938ae", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "344edd71", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2d3664d4", "needsInterop": true}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "456e9c81", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "1305f553", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "258329d1", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "7fb43a3a", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "bf5cd9b7", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "61cfbe2a", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "cc13b886", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "6837ec8b", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "2d0ad48f", "needsInterop": false}, "ziggy-js": {"src": "../../ziggy-js/dist/index.js", "file": "ziggy-js.js", "fileHash": "279605a6", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "5483384a", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "7e937f26", "needsInterop": false}}, "chunks": {"chunk-6KTIIUHH": {"file": "chunk-6KTIIUHH.js"}, "chunk-T23WF632": {"file": "chunk-T23WF632.js"}, "chunk-OOJVNOEJ": {"file": "chunk-OOJVNOEJ.js"}, "chunk-4UBZSK2Y": {"file": "chunk-4UBZSK2Y.js"}, "chunk-KBPUDJB7": {"file": "chunk-KBPUDJB7.js"}, "chunk-R6R6T6DH": {"file": "chunk-R6R6T6DH.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-ZE7GJEYV": {"file": "chunk-ZE7GJEYV.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-7RSYI62Y": {"file": "chunk-7RSYI62Y.js"}, "chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}