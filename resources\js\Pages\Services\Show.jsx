import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { Wrench, ArrowLeft, Edit, Trash2, DollarSign, Tag, Calendar } from 'lucide-react';

export default function Show({ auth, service }) {
    // Mock data if not provided
    const serviceData = service || {
        id: 1,
        name: "Full Service Catering",
        description: "Complete catering service including menu planning, food preparation, setup, service, and cleanup. Our professional team ensures your event runs smoothly from start to finish.",
        price: 45.00,
        price_type: "per_person",
        category: "Catering",
        status: "active",
        created_at: "2024-01-15T10:30:00Z",
        updated_at: "2024-01-20T14:45:00Z"
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'inactive':
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const formatPrice = (price, priceType) => {
        const formattedPrice = `$${price}`;
        switch (priceType) {
            case 'per_person':
                return `${formattedPrice}/person`;
            case 'per_hour':
                return `${formattedPrice}/hour`;
            case 'per_day':
                return `${formattedPrice}/day`;
            case 'flat_rate':
                return formattedPrice;
            default:
                return formattedPrice;
        }
    };

    return (
        <CaterProLayout>
            <Head title={`Service: ${serviceData.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Wrench className="h-6 w-6 mr-3 text-primary" />
                            {serviceData.name}
                        </h1>
                        <p className="text-muted-foreground mt-2">Service details and information</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('services.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Services
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('services.edit', serviceData.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Information */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Service Information</CardTitle>
                                <CardDescription>Details about this service offering</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Service Name</h3>
                                        <p className="text-lg font-semibold">{serviceData.name}</p>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Tag className="h-4 w-4 mr-1" />
                                            Category
                                        </h3>
                                        <p className="text-lg">{serviceData.category}</p>
                                    </div>
                                </div>

                                <Separator />

                                <div>
                                    <h3 className="font-medium text-sm text-muted-foreground mb-2">Description</h3>
                                    <p className="text-foreground leading-relaxed">{serviceData.description}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Service Usage</CardTitle>
                                <CardDescription>Recent orders that included this service</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center py-8 text-muted-foreground">
                                    <Wrench className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>No recent usage</p>
                                    <p className="text-sm">This service hasn't been used in recent orders</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Pricing & Status</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Status</span>
                                    <Badge className={getStatusBadge(serviceData.status)}>
                                        {serviceData.status}
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <DollarSign className="h-4 w-4 mr-1" />
                                        Price
                                    </span>
                                    <span className="text-lg font-semibold">
                                        {formatPrice(serviceData.price, serviceData.price_type)}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Price Type</span>
                                    <span className="text-sm capitalize">
                                        {serviceData.price_type.replace('_', ' ')}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Record Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Created
                                    </span>
                                    <p className="text-sm">{new Date(serviceData.created_at).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </span>
                                    <p className="text-sm">{new Date(serviceData.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </CaterProLayout>
    );
}
