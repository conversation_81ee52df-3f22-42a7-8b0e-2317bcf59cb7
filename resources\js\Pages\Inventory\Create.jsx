import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { Package, ArrowLeft, Save, DollarSign, Hash, FileText } from 'lucide-react';

export default function Create({ auth, suppliers, categories, units }) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        sku: '',
        description: '',
        quantity: '',
        reorder_level: '',
        cost_price: '',
        selling_price: '',
        supplier_id: '',
        inventory_category_id: '',
        inventory_unit_id: '',
    });

    const submit = (e) => {
        e.preventDefault();
        post(route('inventory.store'));
    };

    return (
        <CaterProLayout>
            <Head title="Add Inventory Item" />

            <div className="max-w-4xl mx-auto space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('inventory.index')}>
                            <Button variant="ghost">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Inventory
                            </Button>
                        </Link>
                        <Separator orientation="vertical" className="h-6" />
                        <div>
                            <h1 className="text-3xl font-bold text-foreground flex items-center">
                                <Package className="h-6 w-6 mr-3 text-primary" />
                                Add New Inventory Item
                            </h1>
                            <p className="text-muted-foreground mt-2">Create a new item for your inventory management</p>
                        </div>
                    </div>
                    <Badge variant="secondary">New Item</Badge>
                </div>
                {/* Basic Information Section */}
                <Card className="caterpro-form-section">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <FileText className="h-5 w-5 mr-2 text-primary" />
                            Basic Information
                        </CardTitle>
                        <CardDescription>
                            Enter the basic details for your inventory item
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name" className="text-sm font-medium">Item Name *</Label>
                                    <Input
                                        id="name"
                                        placeholder="e.g., Chicken Breast, Basmati Rice"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.name} className="mt-2" />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="sku" className="text-sm font-medium flex items-center">
                                        <Hash className="h-4 w-4 mr-1" />
                                        SKU (Stock Keeping Unit)
                                    </Label>
                                    <Input
                                        id="sku"
                                        placeholder="e.g., CHK-001, RICE-BAG-5KG"
                                        value={data.sku}
                                        onChange={(e) => setData('sku', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.sku} className="mt-2" />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                                <Textarea
                                    id="description"
                                    placeholder="Detailed description of the item..."
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    rows={3}
                                />
                                <InputError message={errors.description} className="mt-2" />
                            </div>
                        </form>
                    </CardContent>
                </Card>

                {/* Category and Supplier Section */}
                <Card className="caterpro-form-section">
                    <CardHeader>
                        <CardTitle>Classification & Sourcing</CardTitle>
                        <CardDescription>
                            Categorize your item and specify supplier information
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="inventory_category_id" className="text-sm font-medium">Category *</Label>
                                <Select onValueChange={(value) => setData('inventory_category_id', value)} value={data.inventory_category_id}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categories.map(category => (
                                            <SelectItem key={category.id} value={String(category.id)}>{category.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.inventory_category_id} className="mt-2" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="supplier_id" className="text-sm font-medium">Supplier</Label>
                                <Select onValueChange={(value) => setData('supplier_id', value)} value={data.supplier_id}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a supplier" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {suppliers.map(supplier => (
                                            <SelectItem key={supplier.id} value={String(supplier.id)}>{supplier.name}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.supplier_id} className="mt-2" />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="inventory_unit_id" className="text-sm font-medium">Unit of Measurement *</Label>
                                <Select onValueChange={(value) => setData('inventory_unit_id', value)} value={data.inventory_unit_id}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a unit" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {units.map(unit => (
                                            <SelectItem key={unit.id} value={String(unit.id)}>{unit.name} ({unit.abbreviation})</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.inventory_unit_id} className="mt-2" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Inventory & Pricing Section */}
                <Card className="caterpro-form-section">
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <DollarSign className="h-5 w-5 mr-2 text-primary" />
                            Inventory & Pricing
                        </CardTitle>
                        <CardDescription>
                            Set quantities, reorder levels, and pricing information
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="quantity" className="text-sm font-medium">Initial Quantity *</Label>
                                    <Input
                                        id="quantity"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={data.quantity}
                                        onChange={(e) => setData('quantity', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.quantity} className="mt-2" />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="reorder_level" className="text-sm font-medium">Re-order Level</Label>
                                    <Input
                                        id="reorder_level"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={data.reorder_level}
                                        onChange={(e) => setData('reorder_level', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.reorder_level} className="mt-2" />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="cost_price" className="text-sm font-medium">Cost Price *</Label>
                                    <Input
                                        id="cost_price"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={data.cost_price}
                                        onChange={(e) => setData('cost_price', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.cost_price} className="mt-2" />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="selling_price" className="text-sm font-medium">Selling Price</Label>
                                    <Input
                                        id="selling_price"
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={data.selling_price}
                                        onChange={(e) => setData('selling_price', e.target.value)}
                                        className="caterpro-input"
                                    />
                                    <InputError message={errors.selling_price} className="mt-2" />
                                </div>
                            </div>

                            <Separator />

                            <div className="flex items-center justify-between pt-4">
                                <div className="text-sm text-muted-foreground">
                                    * Required fields
                                </div>
                                <div className="flex gap-3">
                                    <Link href={route('inventory.index')}>
                                        <Button variant="outline">
                                            Cancel
                                        </Button>
                                    </Link>
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="caterpro-button-primary"
                                        onClick={submit}
                                    >
                                        <Save className="h-4 w-4 mr-2" />
                                        {processing ? 'Saving...' : 'Save Item'}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}