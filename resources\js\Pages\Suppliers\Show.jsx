import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { But<PERSON> } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Building2, ArrowLeft, Edit, Mail, Phone, MapPin, DollarSign, ShoppingBag, CreditCard } from 'lucide-react';

export default function Show({ auth, supplier }) {
    // Mock data if not provided
    const mockSupplier = supplier || {
        id: 1,
        name: "Fresh Foods Wholesale",
        contact_person: "<PERSON>",
        email: "<EMAIL>",
        phone: "+****************",
        address: "123 Wholesale Ave, Food District, NY 10001",
        created_at: "2024-01-15T10:30:00Z",
        purchases: [
            {
                id: 1,
                purchase_number: "PUR-2024-0001",
                purchase_date: "2024-01-20",
                total_amount: 2500.00,
                paid_amount: 1500.00,
                remaining_amount: 1000.00,
                status: "partial"
            },
            {
                id: 2,
                purchase_number: "PUR-2024-0005",
                purchase_date: "2024-01-10",
                total_amount: 1800.00,
                paid_amount: 1800.00,
                remaining_amount: 0.00,
                status: "paid"
            }
        ],
        payments: [
            {
                id: 1,
                amount: 1500.00,
                payment_date: "2024-01-22",
                payment_method: "bank_transfer",
                reference_number: "TXN-001"
            },
            {
                id: 2,
                amount: 1800.00,
                payment_date: "2024-01-12",
                payment_method: "check",
                reference_number: "CHK-002"
            }
        ]
    };

    const totalPurchases = mockSupplier.purchases?.reduce((sum, purchase) => sum + purchase.total_amount, 0) || 0;
    const totalPaid = mockSupplier.payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0;
    const outstandingBalance = totalPurchases - totalPaid;

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            case 'partial':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'paid':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    return (
        <CaterProLayout>
            <Head title={`Supplier: ${mockSupplier.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Building2 className="h-6 w-6 mr-3 text-primary" />
                            {mockSupplier.name}
                        </h1>
                        <p className="text-muted-foreground mt-2">Supplier details and transaction history</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('suppliers.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Suppliers
                            </Link>
                        </Button>
                        <Button asChild>
                            <Link href={route('suppliers.edit', mockSupplier.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Supplier
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Supplier Information */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Supplier Information</CardTitle>
                                <CardDescription>Contact details and business information</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                                        <p className="text-lg font-semibold">{mockSupplier.name}</p>
                                    </div>
                                    {mockSupplier.contact_person && (
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                                            <p className="text-lg">{mockSupplier.contact_person}</p>
                                        </div>
                                    )}
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {mockSupplier.email && (
                                        <div className="flex items-center">
                                            <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                                            <span>{mockSupplier.email}</span>
                                        </div>
                                    )}
                                    {mockSupplier.phone && (
                                        <div className="flex items-center">
                                            <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                                            <span>{mockSupplier.phone}</span>
                                        </div>
                                    )}
                                </div>

                                {mockSupplier.address && (
                                    <div className="flex items-start">
                                        <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-1" />
                                        <span>{mockSupplier.address}</span>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Financial Summary */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Financial Summary</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Total Purchases</span>
                                    <span className="font-semibold">${totalPurchases.toFixed(2)}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Total Paid</span>
                                    <span className="font-semibold text-green-600">${totalPaid.toFixed(2)}</span>
                                </div>
                                <div className="border-t pt-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium">Outstanding Balance</span>
                                        <span className={`font-bold ${outstandingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                            ${outstandingBalance.toFixed(2)}
                                        </span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full" asChild>
                                    <Link href={route('purchases.create', { supplier_id: mockSupplier.id })}>
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        New Purchase
                                    </Link>
                                </Button>
                                {outstandingBalance > 0 && (
                                    <Button variant="outline" className="w-full" asChild>
                                        <Link href={route('payments.create', { supplier_id: mockSupplier.id })}>
                                            <CreditCard className="h-4 w-4 mr-2" />
                                            Record Payment
                                        </Link>
                                    </Button>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Recent Purchases */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Purchases</CardTitle>
                        <CardDescription>Purchase history with this supplier</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {mockSupplier.purchases && mockSupplier.purchases.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left p-3 font-medium">Purchase #</th>
                                            <th className="text-left p-3 font-medium">Date</th>
                                            <th className="text-left p-3 font-medium">Amount</th>
                                            <th className="text-left p-3 font-medium">Paid</th>
                                            <th className="text-left p-3 font-medium">Outstanding</th>
                                            <th className="text-left p-3 font-medium">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {mockSupplier.purchases.map((purchase) => (
                                            <tr key={purchase.id} className="border-b hover:bg-muted/50">
                                                <td className="p-3 font-medium">{purchase.purchase_number}</td>
                                                <td className="p-3">{new Date(purchase.purchase_date).toLocaleDateString()}</td>
                                                <td className="p-3">${purchase.total_amount.toFixed(2)}</td>
                                                <td className="p-3 text-green-600">${purchase.paid_amount.toFixed(2)}</td>
                                                <td className="p-3">
                                                    <span className={purchase.remaining_amount > 0 ? 'text-red-600' : 'text-green-600'}>
                                                        ${purchase.remaining_amount.toFixed(2)}
                                                    </span>
                                                </td>
                                                <td className="p-3">
                                                    <Badge className={getStatusBadge(purchase.status)}>
                                                        {purchase.status}
                                                    </Badge>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <ShoppingBag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground">No purchases recorded yet</p>
                                <Button className="mt-4" asChild>
                                    <Link href={route('purchases.create', { supplier_id: mockSupplier.id })}>
                                        Create First Purchase
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Recent Payments */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Payments</CardTitle>
                        <CardDescription>Payment history to this supplier</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {mockSupplier.payments && mockSupplier.payments.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b">
                                            <th className="text-left p-3 font-medium">Date</th>
                                            <th className="text-left p-3 font-medium">Amount</th>
                                            <th className="text-left p-3 font-medium">Method</th>
                                            <th className="text-left p-3 font-medium">Reference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {mockSupplier.payments.map((payment) => (
                                            <tr key={payment.id} className="border-b hover:bg-muted/50">
                                                <td className="p-3">{new Date(payment.payment_date).toLocaleDateString()}</td>
                                                <td className="p-3 font-semibold text-green-600">${payment.amount.toFixed(2)}</td>
                                                <td className="p-3 capitalize">{payment.payment_method.replace('_', ' ')}</td>
                                                <td className="p-3">{payment.reference_number || '-'}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground">No payments recorded yet</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
