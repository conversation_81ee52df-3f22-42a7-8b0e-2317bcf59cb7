import { Head, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import {
  ChefHat,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Truck,
  DollarSign,
  Star,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export default function Welcome({ auth, laravelVersion, phpVersion }) {
    const features = [
        {
            icon: Package,
            title: "Smart Inventory Management",
            description: "Track food ingredients and supplies with real-time stock levels, automated alerts, and supplier management."
        },
        {
            icon: ShoppingCart,
            title: "Order Management",
            description: "Streamline order processing from creation to delivery with integrated food and rental services."
        },
        {
            icon: Truck,
            title: "Rental Equipment",
            description: "Manage rental inventory including plates, glasses, furniture with availability tracking and pricing."
        },
        {
            icon: Users,
            title: "Customer Management",
            description: "Maintain customer profiles, order history, and preferences for personalized service."
        },
        {
            icon: BarChart3,
            title: "Analytics & Reports",
            description: "Gain insights with comprehensive reporting on sales, inventory, and business performance."
        },
        {
            icon: DollarSign,
            title: "Financial Tracking",
            description: "Monitor expenses, revenue, and profitability with detailed financial reporting."
        }
    ];

    const benefits = [
        "Reduce food waste with smart inventory tracking",
        "Increase efficiency with automated order processing",
        "Improve customer satisfaction with better service",
        "Maximize profits with detailed analytics",
        "Scale your catering business with confidence"
    ];

    return (
        <>
            <Head title="Welcome to CaterPro" />
            <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
                {/* Navigation */}
                <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <ChefHat className="h-8 w-8 text-primary" />
                                <span className="text-2xl font-bold text-foreground">CaterPro</span>
                                <Badge variant="secondary" className="ml-2">Laravel {laravelVersion}</Badge>
                            </div>
                            <div className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>
                                            Dashboard
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </Button>
                                ) : (
                                    <div className="flex items-center space-x-2">
                                        <Button variant="ghost" asChild>
                                            <Link href={route('login')}>
                                                Log in
                                            </Link>
                                        </Button>
                                        <Button asChild>
                                            <Link href={route('register')}>
                                                Get Started
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <div className="relative">
                    <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="flex justify-center mb-8">
                                <div className="relative">
                                    <div className="absolute inset-0 bg-primary/20 blur-3xl rounded-full"></div>
                                    <ChefHat className="relative h-24 w-24 text-primary mx-auto" />
                                </div>
                            </div>
                            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                                Professional Catering
                                <span className="text-primary"> Management</span>
                            </h1>
                            <p className="mt-6 text-lg leading-8 text-muted-foreground max-w-2xl mx-auto">
                                Streamline your catering business with our comprehensive management system.
                                Handle inventory, orders, rentals, and customer relationships all in one place.
                            </p>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                {!auth.user && (
                                    <>
                                        <Button size="lg" asChild>
                                            <Link href={route('register')}>
                                                Start Free Trial
                                                <ArrowRight className="ml-2 h-5 w-5" />
                                            </Link>
                                        </Button>
                                        <Button variant="outline" size="lg" asChild>
                                            <Link href={route('login')}>
                                                Sign In
                                            </Link>
                                        </Button>
                                    </>
                                )}
                                {auth.user && (
                                    <Button size="lg" asChild>
                                        <Link href={route('dashboard')}>
                                            Go to Dashboard
                                            <ArrowRight className="ml-2 h-5 w-5" />
                                        </Link>
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Features Section */}
                <div className="py-24 bg-muted/30">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                                Everything you need to manage your catering business
                            </h2>
                            <p className="mt-4 text-lg text-muted-foreground">
                                Powerful features designed specifically for catering and event management
                            </p>
                        </div>
                        <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                            {features.map((feature, index) => (
                                <Card key={index} className="relative overflow-hidden">
                                    <CardHeader>
                                        <div className="flex items-center space-x-3">
                                            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                                                <feature.icon className="h-6 w-6 text-primary" />
                                            </div>
                                            <CardTitle className="text-lg">{feature.title}</CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent>
                                        <CardDescription className="text-base">
                                            {feature.description}
                                        </CardDescription>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Benefits Section */}
                <div className="py-24">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 gap-16 lg:grid-cols-2 lg:gap-24">
                            <div>
                                <h2 className="text-3xl font-bold tracking-tight text-foreground">
                                    Why choose CaterPro?
                                </h2>
                                <p className="mt-4 text-lg text-muted-foreground">
                                    Built specifically for catering businesses, CaterPro helps you streamline operations,
                                    reduce costs, and grow your business.
                                </p>
                                <div className="mt-8 space-y-4">
                                    {benefits.map((benefit, index) => (
                                        <div key={index} className="flex items-start space-x-3">
                                            <CheckCircle className="h-6 w-6 text-primary flex-shrink-0 mt-0.5" />
                                            <span className="text-foreground">{benefit}</span>
                                        </div>
                                    ))}
                                </div>
                                {!auth.user && (
                                    <div className="mt-8">
                                        <Button size="lg" asChild>
                                            <Link href={route('register')}>
                                                Get Started Today
                                                <ArrowRight className="ml-2 h-5 w-5" />
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                            <div className="relative">
                                <div className="aspect-square rounded-2xl bg-gradient-to-br from-primary/20 to-accent/20 p-8">
                                    <div className="grid grid-cols-2 gap-4 h-full">
                                        <Card className="flex items-center justify-center">
                                            <div className="text-center">
                                                <Package className="h-8 w-8 text-primary mx-auto mb-2" />
                                                <div className="text-2xl font-bold">156</div>
                                                <div className="text-sm text-muted-foreground">Items</div>
                                            </div>
                                        </Card>
                                        <Card className="flex items-center justify-center">
                                            <div className="text-center">
                                                <ShoppingCart className="h-8 w-8 text-primary mx-auto mb-2" />
                                                <div className="text-2xl font-bold">23</div>
                                                <div className="text-sm text-muted-foreground">Orders</div>
                                            </div>
                                        </Card>
                                        <Card className="flex items-center justify-center">
                                            <div className="text-center">
                                                <Users className="h-8 w-8 text-primary mx-auto mb-2" />
                                                <div className="text-2xl font-bold">89</div>
                                                <div className="text-sm text-muted-foreground">Customers</div>
                                            </div>
                                        </Card>
                                        <Card className="flex items-center justify-center">
                                            <div className="text-center">
                                                <Star className="h-8 w-8 text-primary mx-auto mb-2" />
                                                <div className="text-2xl font-bold">4.9</div>
                                                <div className="text-sm text-muted-foreground">Rating</div>
                                            </div>
                                        </Card>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <footer className="border-t bg-muted/30">
                    <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <ChefHat className="h-6 w-6 text-primary" />
                                <span className="text-lg font-semibold">CaterPro</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                                Laravel v{laravelVersion} (PHP v{phpVersion}) • Built with ❤️ for catering professionals
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
