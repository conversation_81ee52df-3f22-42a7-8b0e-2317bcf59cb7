import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import {
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Plus,
  Calendar,
  Wrench,
  BarChart3
} from 'lucide-react';

export default function Dashboard() {
    // Enhanced mock data - in real app this would come from props
    const stats = [
        {
            title: "Total Revenue",
            value: "$15,750",
            change: "+18.2%",
            trend: "up",
            icon: DollarSign,
            color: "text-green-600",
            bgColor: "bg-green-100",
            description: "This month"
        },
        {
            title: "Active Orders",
            value: "12",
            change: "+4",
            trend: "up",
            icon: ShoppingCart,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            description: "In progress"
        },
        {
            title: "Inventory Items",
            value: "156",
            change: "-2",
            trend: "down",
            icon: Package,
            color: "text-orange-600",
            bgColor: "bg-orange-100",
            description: "8 low stock"
        },
        {
            title: "Total Customers",
            value: "89",
            change: "+12",
            trend: "up",
            icon: Users,
            color: "text-purple-600",
            bgColor: "bg-purple-100",
            description: "7 new this month"
        }
    ];

    const recentOrders = [
        {
            id: 1,
            customer: "Sarah Johnson",
            event: "Wedding Reception",
            amount: "$2,500",
            status: "confirmed",
            date: "2024-02-15",
            guests: 150
        },
        {
            id: 2,
            customer: "TechCorp Inc",
            event: "Corporate Meeting",
            amount: "$850",
            status: "in_progress",
            date: "2024-02-10",
            guests: 45
        },
        {
            id: 3,
            customer: "Mike Thompson",
            event: "Birthday Party",
            amount: "$650",
            status: "pending",
            date: "2024-02-20",
            guests: 25
        },
        {
            id: 4,
            customer: "Lisa Chen",
            event: "Anniversary Dinner",
            amount: "$450",
            status: "completed",
            date: "2024-02-08",
            guests: 12
        },
    ];

    const lowStockItems = [
        { name: "Chicken Breast", current: 5, minimum: 10, unit: "lbs" },
        { name: "Basmati Rice", current: 2, minimum: 5, unit: "bags" },
        { name: "Dinner Plates", current: 15, minimum: 20, unit: "pieces" },
        { name: "Round Tables", current: 3, minimum: 8, unit: "units" },
    ];

    const quickActions = [
        {
            title: "New Order",
            description: "Create a new catering order",
            icon: Plus,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            href: "orders.create"
        },
        {
            title: "Add Inventory",
            description: "Add new inventory item",
            icon: Package,
            color: "text-green-600",
            bgColor: "bg-green-100",
            href: "inventory.create"
        },
        {
            title: "View Reports",
            description: "Check business analytics",
            icon: BarChart3,
            color: "text-purple-600",
            bgColor: "bg-purple-100",
            href: "reports.index"
        },
        {
            title: "Add Service",
            description: "Create new service offering",
            icon: Wrench,
            color: "text-orange-600",
            bgColor: "bg-orange-100",
            href: "services.create"
        }
    ];

    const upcomingEvents = [
        {
            id: 1,
            name: "Wedding Reception",
            customer: "Sarah Johnson",
            date: "2024-02-15",
            time: "6:00 PM",
            status: "confirmed"
        },
        {
            id: 2,
            name: "Corporate Meeting",
            customer: "TechCorp Inc",
            date: "2024-02-10",
            time: "12:00 PM",
            status: "in_progress"
        },
        {
            id: 3,
            name: "Birthday Party",
            customer: "Mike Thompson",
            date: "2024-02-20",
            time: "3:00 PM",
            status: "pending"
        }
    ];

    const getStatusBadge = (status) => {
        const variants = {
            pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
            confirmed: "bg-blue-100 text-blue-800 border-blue-200",
            in_progress: "bg-orange-100 text-orange-800 border-orange-200",
            completed: "bg-green-100 text-green-800 border-green-200"
        };
        return variants[status] || variants.pending;
    };

    return (
        <CaterProLayout>
            <Head title="Dashboard" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
                        <p className="text-muted-foreground mt-2">Welcome back! Here's what's happening with your catering business.</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('reports.index')}>
                                <BarChart3 className="h-4 w-4 mr-2" />
                                View Reports
                            </Link>
                        </Button>
                        <Button asChild>
                            <Link href={route('orders.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                New Order
                            </Link>
                        </Button>
                    </div>
                </div>
                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {stats.map((stat, index) => (
                        <Card key={index} className="hover:shadow-md transition-shadow">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                                        <p className="text-2xl font-bold">{stat.value}</p>
                                        <div className="flex items-center mt-1">
                                            <TrendingUp className={`mr-1 h-4 w-4 ${stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
                                            <span className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                                                {stat.change}
                                            </span>
                                        </div>
                                        <p className="text-xs text-muted-foreground mt-1">{stat.description}</p>
                                    </div>
                                    <div className={`h-12 w-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                                        <stat.icon className={`h-6 w-6 ${stat.color}`} />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common tasks and shortcuts</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {quickActions.map((action, index) => (
                                <Link key={index} href={route(action.href)} className="block">
                                    <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                                        <div className={`h-10 w-10 rounded-lg ${action.bgColor} flex items-center justify-center mb-3`}>
                                            <action.icon className={`h-5 w-5 ${action.color}`} />
                                        </div>
                                        <h3 className="font-medium text-sm">{action.title}</h3>
                                        <p className="text-xs text-muted-foreground mt-1">{action.description}</p>
                                    </div>
                                </Link>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Recent Orders */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Orders</CardTitle>
                            <CardDescription>Latest customer orders and their status</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentOrders.map((order) => (
                                    <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                                        <div className="flex items-center">
                                            <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                <ShoppingCart className="h-5 w-5 text-primary" />
                                            </div>
                                            <div>
                                                <p className="font-medium">{order.customer}</p>
                                                <p className="text-sm text-muted-foreground">{order.event}</p>
                                                <p className="text-xs text-muted-foreground">{order.guests} guests • {order.date}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <div className="text-right">
                                                <span className="font-medium">{order.amount}</span>
                                                <Badge className={`${getStatusBadge(order.status)} ml-2`}>
                                                    {order.status.replace('_', ' ')}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="mt-4">
                                <Button variant="outline" className="w-full" asChild>
                                    <Link href={route('orders.index')}>
                                        View All Orders
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Low Stock Alert */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-orange-500" />
                                Low Stock Alert
                            </CardTitle>
                            <CardDescription>Items that need restocking</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {lowStockItems.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-orange-50 hover:bg-orange-100 transition-colors">
                                        <div className="flex items-center">
                                            <div className="h-10 w-10 rounded-lg bg-orange-200 flex items-center justify-center mr-3">
                                                <Package className="h-5 w-5 text-orange-600" />
                                            </div>
                                            <div>
                                                <p className="font-medium">{item.name}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    Current: {item.current} {item.unit} • Min: {item.minimum} {item.unit}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <Button size="sm" variant="outline" asChild>
                                                <Link href={route('inventory.index')}>
                                                    Restock
                                                </Link>
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="mt-4">
                                <Button variant="outline" className="w-full" asChild>
                                    <Link href={route('inventory.index')}>
                                        View All Inventory
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Upcoming Events */}
                <Card>
                    <CardHeader>
                        <CardTitle>Upcoming Events</CardTitle>
                        <CardDescription>Events scheduled for the next few days</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {upcomingEvents.map((event) => (
                                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                                    <div className="flex items-center">
                                        <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
                                            <Calendar className="h-5 w-5 text-blue-600" />
                                        </div>
                                        <div>
                                            <p className="font-medium">{event.name}</p>
                                            <p className="text-sm text-muted-foreground">{event.customer}</p>
                                            <p className="text-xs text-muted-foreground">{event.date} at {event.time}</p>
                                        </div>
                                    </div>
                                    <div>
                                        <Badge className={getStatusBadge(event.status)}>
                                            {event.status.replace('_', ' ')}
                                        </Badge>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="mt-4">
                            <Button variant="outline" className="w-full" asChild>
                                <Link href={route('orders.index')}>
                                    View All Events
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
