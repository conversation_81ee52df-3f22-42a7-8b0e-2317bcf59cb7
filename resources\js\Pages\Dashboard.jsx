import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import {
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Plus,
  Eye
} from 'lucide-react';

export default function Dashboard() {
    // Mock data - in real app this would come from props
    const stats = [
        {
            title: "Total Revenue",
            value: "$12,345",
            change: "+12.5%",
            trend: "up",
            icon: DollarSign,
            color: "text-green-600"
        },
        {
            title: "Active Orders",
            value: "23",
            change: "+3",
            trend: "up",
            icon: ShoppingCart,
            color: "text-blue-600"
        },
        {
            title: "Inventory Items",
            value: "156",
            change: "-2",
            trend: "down",
            icon: Package,
            color: "text-orange-600"
        },
        {
            title: "Customers",
            value: "89",
            change: "+7",
            trend: "up",
            icon: Users,
            color: "text-purple-600"
        }
    ];

    const recentOrders = [
        { id: 1, customer: "John Doe", amount: "$245", status: "pending", date: "2025-06-17" },
        { id: 2, customer: "Jane Smith", amount: "$189", status: "confirmed", date: "2025-06-17" },
        { id: 3, customer: "Bob Johnson", amount: "$356", status: "in_progress", date: "2025-06-16" },
        { id: 4, customer: "Alice Brown", amount: "$123", status: "completed", date: "2025-06-16" },
    ];

    const lowStockItems = [
        { name: "Chicken Breast", current: 5, minimum: 10, unit: "lbs" },
        { name: "Basmati Rice", current: 2, minimum: 5, unit: "bags" },
        { name: "Dinner Plates", current: 15, minimum: 20, unit: "pieces" },
    ];

    const getStatusBadge = (status) => {
        const variants = {
            pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
            confirmed: "bg-blue-100 text-blue-800 border-blue-200",
            in_progress: "bg-orange-100 text-orange-800 border-orange-200",
            completed: "bg-green-100 text-green-800 border-green-200"
        };
        return variants[status] || variants.pending;
    };

    return (
        <CaterProLayout>
            <Head title="Dashboard" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
                        <p className="text-muted-foreground mt-2">Welcome back! Here's what's happening with your catering business.</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            View Reports
                        </Button>
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            New Order
                        </Button>
                    </div>
                </div>
                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {stats.map((stat, index) => (
                        <Card key={index} className="caterpro-stats-card">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">
                                    {stat.title}
                                </CardTitle>
                                <stat.icon className={`h-4 w-4 ${stat.color}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value}</div>
                                <div className="flex items-center text-xs text-muted-foreground">
                                    <TrendingUp className={`mr-1 h-3 w-3 ${stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
                                    {stat.change} from last month
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Recent Orders */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Orders</CardTitle>
                            <CardDescription>Latest customer orders and their status</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentOrders.map((order) => (
                                    <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="font-medium">{order.customer}</p>
                                            <p className="text-sm text-muted-foreground">{order.date}</p>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <span className="font-medium">{order.amount}</span>
                                            <Badge className={getStatusBadge(order.status)}>
                                                {order.status.replace('_', ' ')}
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Low Stock Alert */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-orange-500" />
                                Low Stock Alert
                            </CardTitle>
                            <CardDescription>Items that need restocking</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {lowStockItems.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-orange-50">
                                        <div>
                                            <p className="font-medium">{item.name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Current: {item.current} {item.unit}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm text-orange-600 font-medium">
                                                Min: {item.minimum} {item.unit}
                                            </p>
                                            <Button size="sm" variant="outline" className="mt-1">
                                                Restock
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </CaterProLayout>
    );
}
