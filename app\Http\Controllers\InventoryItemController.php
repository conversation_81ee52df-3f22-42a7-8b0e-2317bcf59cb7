<?php

namespace App\Http\Controllers;

use App\Models\InventoryCategory;
use App\Models\InventoryItem;
use App\Models\InventoryUnit;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Inertia\Inertia;

class InventoryItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $inventoryItems = InventoryItem::with(['supplier', 'category', 'unit'])->latest()->paginate(10);

        return Inertia::render('Inventory/Index', [
            'inventoryItems' => $inventoryItems,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Inventory/Create', [
            'suppliers' => Supplier::all(),
            'categories' => InventoryCategory::all(),
            'units' => InventoryUnit::all(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:inventory_items',
            'description' => 'nullable|string',
            'quantity' => 'required|numeric|min:0',
            'reorder_level' => 'required|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'supplier_id' => 'required|exists:suppliers,id',
            'inventory_category_id' => 'required|exists:inventory_categories,id',
            'inventory_unit_id' => 'required|exists:inventory_units,id',
        ]);

        InventoryItem::create($validated);

        return redirect(route('inventory.index'))->with('success', 'Inventory item created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(InventoryItem $inventoryItem)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(InventoryItem $inventoryItem)
    {
        return Inertia::render('Inventory/Edit', [
            'item' => $inventoryItem->load(['supplier', 'category', 'unit']),
            'suppliers' => Supplier::all(),
            'categories' => InventoryCategory::all(),
            'units' => InventoryUnit::all(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, InventoryItem $inventoryItem)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:255|unique:inventory_items,sku,' . $inventoryItem->id,
            'description' => 'nullable|string',
            'quantity' => 'required|numeric|min:0',
            'reorder_level' => 'required|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'supplier_id' => 'required|exists:suppliers,id',
            'inventory_category_id' => 'required|exists:inventory_categories,id',
            'inventory_unit_id' => 'required|exists:inventory_units,id',
        ]);

        $inventoryItem->update($validated);

        return redirect(route('inventory.index'))->with('success', 'Inventory item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(InventoryItem $inventoryItem)
    {
        $inventoryItem->delete();

        return redirect(route('inventory.index'))->with('success', 'Inventory item deleted successfully.');
    }
}
