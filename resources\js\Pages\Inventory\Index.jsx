import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Package, Plus, Search, Filter } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, inventoryItems = { data: [], links: [] } }) {
    // Mock data for now since the controller might not be implemented yet
    const mockItems = inventoryItems.data || [
        {
            id: 1,
            name: "Chicken Breast",
            sku: "CHK-001",
            quantity: 25,
            unit: "lbs",
            cost_price: 5.99,
            category: "Meat",
            status: "in_stock"
        },
        {
            id: 2,
            name: "<PERSON>sma<PERSON>",
            sku: "RICE-001",
            quantity: 3,
            unit: "bags",
            cost_price: 12.50,
            category: "Grains",
            status: "low_stock"
        },
        {
            id: 3,
            name: "Dinner Plates",
            sku: "PLATE-001",
            quantity: 45,
            unit: "pieces",
            cost_price: 8.99,
            category: "Equipment",
            status: "in_stock"
        }
    ];

    const getStatusBadge = (status) => {
        const variants = {
            in_stock: "bg-green-100 text-green-800 border-green-200",
            low_stock: "bg-yellow-100 text-yellow-800 border-yellow-200",
            out_of_stock: "bg-red-100 text-red-800 border-red-200"
        };
        return variants[status] || variants.in_stock;
    };

    return (
        <CaterProLayout>
            <Head title="Inventory" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Package className="h-6 w-6 mr-3 text-primary" />
                            Inventory Management
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your food ingredients and equipment inventory</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('inventory.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add New Item
                            </Link>
                        </Button>
                    </div>
                </div>
                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Inventory</CardTitle>
                        <CardDescription>Find items by name, SKU, or category</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search by name or SKU..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Inventory Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Inventory Items</CardTitle>
                        <CardDescription>
                            {mockItems.length} items in inventory
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full caterpro-table">
                                <thead>
                                    <tr className="caterpro-table-header">
                                        <th className="text-left p-3">Item</th>
                                        <th className="text-left p-3">SKU</th>
                                        <th className="text-left p-3">Category</th>
                                        <th className="text-left p-3">Quantity</th>
                                        <th className="text-left p-3">Cost Price</th>
                                        <th className="text-left p-3">Status</th>
                                        <th className="text-left p-3">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockItems.map((item) => (
                                        <tr key={item.id} className="caterpro-table-row">
                                            <td className="p-3">
                                                <div>
                                                    <div className="font-medium">{item.name}</div>
                                                </div>
                                            </td>
                                            <td className="p-3 text-muted-foreground">{item.sku}</td>
                                            <td className="p-3">{item.category}</td>
                                            <td className="p-3">
                                                {item.quantity} {item.unit}
                                            </td>
                                            <td className="p-3">${item.cost_price}</td>
                                            <td className="p-3">
                                                <Badge className={getStatusBadge(item.status)}>
                                                    {item.status.replace('_', ' ')}
                                                </Badge>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('inventory.show', item.id)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('inventory.edit', item.id)}>
                                                            Edit
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}