import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { ShoppingBag, Plus, Search, Filter, Calendar, Building2, DollarSign, Receipt } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, purchases = { data: [], links: [] } }) {
    // Mock data for now
    const mockPurchases = purchases.data || [
        {
            id: 1,
            purchase_number: "PUR-2024-0001",
            supplier: { name: "Fresh Foods Wholesale", contact_person: "<PERSON>" },
            purchase_date: "2024-01-20",
            total_amount: 2500.00,
            paid_amount: 1500.00,
            remaining_amount: 1000.00,
            status: "partial",
            invoice_number: "INV-FF-2024-001",
            items_count: 5
        },
        {
            id: 2,
            purchase_number: "PUR-2024-0002",
            supplier: { name: "Premium Meat Suppliers", contact_person: "<PERSON>" },
            purchase_date: "2024-01-18",
            total_amount: 1800.00,
            paid_amount: 1800.00,
            remaining_amount: 0.00,
            status: "paid",
            invoice_number: "INV-PM-2024-002",
            items_count: 3
        },
        {
            id: 3,
            purchase_number: "PUR-2024-0003",
            supplier: { name: "Dairy & Produce Co.", contact_person: "Mike Chen" },
            purchase_date: "2024-01-15",
            total_amount: 3200.00,
            paid_amount: 0.00,
            remaining_amount: 3200.00,
            status: "pending",
            invoice_number: "INV-DP-2024-003",
            items_count: 8
        },
        {
            id: 4,
            purchase_number: "PUR-2024-0004",
            supplier: { name: "Equipment & Supplies Ltd", contact_person: "Lisa Rodriguez" },
            purchase_date: "2024-01-12",
            total_amount: 950.00,
            paid_amount: 500.00,
            remaining_amount: 450.00,
            status: "partial",
            invoice_number: "INV-ES-2024-004",
            items_count: 2
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            case 'partial':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'paid':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const totalPurchases = mockPurchases.length;
    const totalAmount = mockPurchases.reduce((sum, purchase) => sum + purchase.total_amount, 0);
    const totalOutstanding = mockPurchases.reduce((sum, purchase) => sum + purchase.remaining_amount, 0);

    return (
        <CaterProLayout>
            <Head title="Purchases" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <ShoppingBag className="h-6 w-6 mr-3 text-primary" />
                            Purchases
                        </h1>
                        <p className="text-muted-foreground mt-2">Track inventory purchases and manage supplier payments</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('purchases.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                New Purchase
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                                    <p className="text-2xl font-bold">{totalPurchases}</p>
                                </div>
                                <ShoppingBag className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                                    <p className="text-2xl font-bold">${totalAmount.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Outstanding</p>
                                    <p className="text-2xl font-bold text-red-600">${totalOutstanding.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-red-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Purchases</CardTitle>
                        <CardDescription>Find purchases by number, supplier, or invoice</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search purchases..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Purchases Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Purchases ({mockPurchases.length})</CardTitle>
                        <CardDescription>All your inventory purchases and payment status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Purchase</th>
                                        <th className="text-left p-3 font-medium">Supplier</th>
                                        <th className="text-left p-3 font-medium">Date</th>
                                        <th className="text-left p-3 font-medium">Amount</th>
                                        <th className="text-left p-3 font-medium">Outstanding</th>
                                        <th className="text-left p-3 font-medium">Status</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockPurchases.map((purchase) => (
                                        <tr key={purchase.id} className="border-b hover:bg-muted/50">
                                            <td className="p-3">
                                                <div className="flex items-center">
                                                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                        <ShoppingBag className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{purchase.purchase_number}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {purchase.items_count} items
                                                        </div>
                                                        {purchase.invoice_number && (
                                                            <div className="text-xs text-muted-foreground flex items-center mt-1">
                                                                <Receipt className="h-3 w-3 mr-1" />
                                                                {purchase.invoice_number}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex items-center">
                                                    <Building2 className="h-4 w-4 mr-2 text-muted-foreground" />
                                                    <div>
                                                        <div className="font-medium">{purchase.supplier.name}</div>
                                                        {purchase.supplier.contact_person && (
                                                            <div className="text-sm text-muted-foreground">
                                                                {purchase.supplier.contact_person}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex items-center text-sm text-muted-foreground">
                                                    <Calendar className="h-4 w-4 mr-2" />
                                                    {new Date(purchase.purchase_date).toLocaleDateString()}
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <div>
                                                    <div className="font-semibold">${purchase.total_amount.toFixed(2)}</div>
                                                    <div className="text-sm text-muted-foreground">
                                                        Paid: ${purchase.paid_amount.toFixed(2)}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <span className={`font-semibold ${purchase.remaining_amount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                                    ${purchase.remaining_amount.toFixed(2)}
                                                </span>
                                            </td>
                                            <td className="p-3">
                                                <Badge className={getStatusBadge(purchase.status)}>
                                                    {purchase.status}
                                                </Badge>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('purchases.show', purchase.id)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    {purchase.remaining_amount > 0 && (
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('payments.create', { purchase_id: purchase.id })}>
                                                                Pay
                                                            </Link>
                                                        </Button>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
