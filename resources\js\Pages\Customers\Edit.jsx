import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { Users, ArrowLeft, Save, Mail, Phone, MapPin, Building } from 'lucide-react';

export default function Edit({ auth, customer }) {
    // Mock data if not provided
    const customerData = customer || {
        id: 1,
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "+****************",
        company: "Johnson Events",
        address: "123 Main St, Downtown, NY 10001",
        notes: "Prefers vegetarian options for all events."
    };

    const { data, setData, put, processing, errors } = useForm({
        name: customerData.name || '',
        email: customerData.email || '',
        phone: customerData.phone || '',
        address: customerData.address || '',
        company: customerData.company || '',
        notes: customerData.notes || '',
    });

    const submit = (e) => {
        e.preventDefault();
        put(route('customers.update', customerData.id));
    };

    return (
        <CaterProLayout>
            <Head title={`Edit Customer: ${customerData.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Users className="h-6 w-6 mr-3 text-primary" />
                            Edit Customer
                        </h1>
                        <p className="text-muted-foreground mt-2">Update customer information</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('customers.show', customerData.id)}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Customer
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                            <CardDescription>Update the customer's basic contact information</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Full Name *</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Sarah Johnson"
                                        required
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="email">Email Address *</Label>
                                    <div className="relative">
                                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="e.g., <EMAIL>"
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.email} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <div className="relative">
                                        <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="phone"
                                            type="tel"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="e.g., +****************"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.phone} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="company">Company/Organization</Label>
                                    <div className="relative">
                                        <Building className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="company"
                                            type="text"
                                            value={data.company}
                                            onChange={(e) => setData('company', e.target.value)}
                                            placeholder="e.g., Johnson Events"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.company} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                <div className="relative">
                                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        placeholder="Enter full address..."
                                        className="pl-10"
                                        rows={3}
                                    />
                                </div>
                                <InputError message={errors.address} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Additional Information</CardTitle>
                            <CardDescription>Update notes and preferences</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes & Preferences</Label>
                                <Textarea
                                    id="notes"
                                    value={data.notes}
                                    onChange={(e) => setData('notes', e.target.value)}
                                    placeholder="Any special notes, dietary preferences, or important information about this customer..."
                                    rows={4}
                                />
                                <InputError message={errors.notes} />
                            </div>

                            <div className="bg-muted/50 p-4 rounded-lg">
                                <h4 className="font-medium mb-2">Customer Type</h4>
                                <p className="text-sm text-muted-foreground">
                                    {data.company ? (
                                        <>
                                            <span className="font-medium text-blue-600">Business Customer</span> - 
                                            This customer will be categorized as a business client.
                                        </>
                                    ) : (
                                        <>
                                            <span className="font-medium text-green-600">Individual Customer</span> - 
                                            This customer will be categorized as an individual client.
                                        </>
                                    )}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('customers.show', customerData.id)}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Updating...' : 'Update Customer'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
