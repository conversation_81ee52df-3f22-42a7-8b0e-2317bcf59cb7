import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Truck, Plus, Search, Filter } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, rentalItems = { data: [], links: [] } }) {
    // Mock data for now since the controller might not be implemented yet
    const mockItems = rentalItems.data || [
        {
            id: 1,
            name: "Round Tables (8-person)",
            sku: "TBL-R8-001",
            quantity: 15,
            daily_rate: 25.00,
            status: "available",
            description: "High-quality round tables perfect for events"
        },
        {
            id: 2,
            name: "<PERSON>avari Chairs",
            sku: "CHR-CHI-001",
            quantity: 120,
            daily_rate: 3.50,
            status: "available",
            description: "Elegant gold chiavari chairs"
        },
        {
            id: 3,
            name: "Sound System Package",
            sku: "SND-PKG-001",
            quantity: 2,
            daily_rate: 150.00,
            status: "rented",
            description: "Complete sound system with microphones"
        },
        {
            id: 4,
            name: "Tent 20x30",
            sku: "TNT-20X30-001",
            quantity: 1,
            daily_rate: 300.00,
            status: "maintenance",
            description: "Large white event tent"
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'available':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'rented':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'maintenance':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    return (
        <CaterProLayout>
            <Head title="Rental Inventory" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Truck className="h-6 w-6 mr-3 text-primary" />
                            Rental Inventory
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your rental equipment and furniture inventory</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('rentals.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Rental Item
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Rental Items</CardTitle>
                        <CardDescription>Find items by name, SKU, or status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search rental items..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Rental Items Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Rental Items ({mockItems.length})</CardTitle>
                        <CardDescription>All your rental equipment and furniture</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Item</th>
                                        <th className="text-left p-3 font-medium">SKU</th>
                                        <th className="text-left p-3 font-medium">Quantity</th>
                                        <th className="text-left p-3 font-medium">Daily Rate</th>
                                        <th className="text-left p-3 font-medium">Status</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockItems.map((item) => (
                                        <tr key={item.id} className="border-b hover:bg-muted/50">
                                            <td className="p-3">
                                                <div className="flex items-center">
                                                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                        <Truck className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{item.name}</div>
                                                        <div className="text-sm text-muted-foreground">{item.description}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3 text-muted-foreground">{item.sku}</td>
                                            <td className="p-3">{item.quantity} units</td>
                                            <td className="p-3">${item.daily_rate}/day</td>
                                            <td className="p-3">
                                                <Badge className={getStatusBadge(item.status)}>
                                                    {item.status}
                                                </Badge>
                                            </td>
                                            <td className="p-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('rentals.show', item.id)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('rentals.edit', item.id)}>
                                                            Edit
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
