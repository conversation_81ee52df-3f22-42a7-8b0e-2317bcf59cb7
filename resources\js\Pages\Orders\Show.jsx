import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { <PERSON><PERSON> } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { 
    ShoppingCart, 
    ArrowLeft, 
    Edit, 
    Trash2, 
    Calendar, 
    MapPin, 
    Users, 
    DollarSign,
    Mail,
    Phone
} from 'lucide-react';

export default function Show({ auth, order }) {
    // Mock data if not provided
    const orderData = order || {
        id: 1,
        customer: {
            name: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            company: "Johnson Events"
        },
        event_name: "Annual Company Retreat",
        event_date: "2024-02-15T18:00:00Z",
        event_address: "Grand Ballroom, Downtown Hotel, 123 Main St, Downtown, NY 10001",
        total_amount: 2500.00,
        status: "confirmed",
        notes: "Vegetarian options required for 30% of guests. Setup required by 5:30 PM. Contact person will be on-site from 5:00 PM.",
        created_at: "2024-01-20T10:30:00Z",
        updated_at: "2024-01-22T14:45:00Z"
    };

    // Mock order items
    const orderItems = [
        {
            id: 1,
            type: "food",
            name: "Chicken Biryani",
            quantity: 100,
            unit_price: 15.00,
            total_price: 1500.00,
            notes: "Medium spice level"
        },
        {
            id: 2,
            type: "food", 
            name: "Vegetable Curry",
            quantity: 50,
            unit_price: 12.00,
            total_price: 600.00,
            notes: "Vegan option"
        },
        {
            id: 3,
            type: "rental",
            name: "Round Tables (8-person)",
            quantity: 15,
            unit_price: 25.00,
            rental_days: 1,
            total_price: 375.00,
            notes: "White tablecloths included"
        },
        {
            id: 4,
            type: "rental",
            name: "Chiavari Chairs",
            quantity: 120,
            unit_price: 3.50,
            rental_days: 1,
            total_price: 420.00,
            notes: "Gold finish"
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            case 'confirmed':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'in_progress':
                return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
            case 'completed':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'cancelled':
                return 'bg-red-100 text-red-800 hover:bg-red-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const foodItems = orderItems.filter(item => item.type === 'food');
    const rentalItems = orderItems.filter(item => item.type === 'rental');
    const foodTotal = foodItems.reduce((sum, item) => sum + item.total_price, 0);
    const rentalTotal = rentalItems.reduce((sum, item) => sum + item.total_price, 0);

    return (
        <CaterProLayout>
            <Head title={`Order #${orderData.id} - ${orderData.event_name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <ShoppingCart className="h-6 w-6 mr-3 text-primary" />
                            Order #{orderData.id}
                        </h1>
                        <p className="text-muted-foreground mt-2">{orderData.event_name}</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('orders.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Orders
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('orders.edit', orderData.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Information */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Order Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Order Information</CardTitle>
                                <CardDescription>Event and customer details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Event Name</h3>
                                        <p className="text-lg font-semibold">{orderData.event_name}</p>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Calendar className="h-4 w-4 mr-1" />
                                            Event Date
                                        </h3>
                                        <p className="text-lg">{new Date(orderData.event_date).toLocaleString()}</p>
                                    </div>
                                </div>

                                <div>
                                    <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                        <MapPin className="h-4 w-4 mr-1" />
                                        Event Address
                                    </h3>
                                    <p className="text-foreground">{orderData.event_address}</p>
                                </div>

                                <Separator />

                                <div>
                                    <h3 className="font-medium text-sm text-muted-foreground mb-2">Customer Information</h3>
                                    <div className="space-y-2">
                                        <p className="font-medium">{orderData.customer.name}</p>
                                        {orderData.customer.company && (
                                            <p className="text-sm text-muted-foreground">{orderData.customer.company}</p>
                                        )}
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Mail className="h-4 w-4 mr-2" />
                                            {orderData.customer.email}
                                        </div>
                                        {orderData.customer.phone && (
                                            <div className="flex items-center text-sm text-muted-foreground">
                                                <Phone className="h-4 w-4 mr-2" />
                                                {orderData.customer.phone}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {orderData.notes && (
                                    <>
                                        <Separator />
                                        <div>
                                            <h3 className="font-medium text-sm text-muted-foreground mb-2">Special Notes</h3>
                                            <p className="text-foreground leading-relaxed">{orderData.notes}</p>
                                        </div>
                                    </>
                                )}
                            </CardContent>
                        </Card>

                        {/* Order Items */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Order Items</CardTitle>
                                <CardDescription>Food services and rental items</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Food Items */}
                                {foodItems.length > 0 && (
                                    <div>
                                        <h4 className="font-medium mb-3">Food Services</h4>
                                        <div className="space-y-3">
                                            {foodItems.map((item) => (
                                                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{item.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {item.quantity} servings × ${item.unit_price}
                                                        </p>
                                                        {item.notes && (
                                                            <p className="text-xs text-muted-foreground mt-1">{item.notes}</p>
                                                        )}
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-semibold">${item.total_price.toFixed(2)}</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="flex justify-between items-center mt-3 pt-3 border-t">
                                            <span className="font-medium">Food Services Subtotal:</span>
                                            <span className="font-semibold">${foodTotal.toFixed(2)}</span>
                                        </div>
                                    </div>
                                )}

                                {/* Rental Items */}
                                {rentalItems.length > 0 && (
                                    <div>
                                        <h4 className="font-medium mb-3">Rental Items</h4>
                                        <div className="space-y-3">
                                            {rentalItems.map((item) => (
                                                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div>
                                                        <p className="font-medium">{item.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {item.quantity} units × ${item.unit_price}/day × {item.rental_days} day(s)
                                                        </p>
                                                        {item.notes && (
                                                            <p className="text-xs text-muted-foreground mt-1">{item.notes}</p>
                                                        )}
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-semibold">${item.total_price.toFixed(2)}</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="flex justify-between items-center mt-3 pt-3 border-t">
                                            <span className="font-medium">Rental Items Subtotal:</span>
                                            <span className="font-semibold">${rentalTotal.toFixed(2)}</span>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Order Status</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Current Status</span>
                                    <Badge className={getStatusBadge(orderData.status)}>
                                        {orderData.status.replace('_', ' ')}
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <DollarSign className="h-4 w-4 mr-1" />
                                        Total Amount
                                    </span>
                                    <span className="text-lg font-semibold">
                                        ${orderData.total_amount.toFixed(2)}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Order Summary</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {foodItems.length > 0 && (
                                    <div className="flex justify-between">
                                        <span className="text-sm">Food Services:</span>
                                        <span className="text-sm font-medium">${foodTotal.toFixed(2)}</span>
                                    </div>
                                )}
                                {rentalItems.length > 0 && (
                                    <div className="flex justify-between">
                                        <span className="text-sm">Rental Items:</span>
                                        <span className="text-sm font-medium">${rentalTotal.toFixed(2)}</span>
                                    </div>
                                )}
                                <Separator />
                                <div className="flex justify-between">
                                    <span className="font-medium">Total:</span>
                                    <span className="font-semibold text-lg">${orderData.total_amount.toFixed(2)}</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Record Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Order Created
                                    </span>
                                    <p className="text-sm">{new Date(orderData.created_at).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </span>
                                    <p className="text-sm">{new Date(orderData.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </CaterProLayout>
    );
}
