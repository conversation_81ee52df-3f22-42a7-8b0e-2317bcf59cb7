import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Building2, Plus, Search, Filter, Mail, Phone, MapPin, DollarSign } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, suppliers = { data: [], links: [] } }) {
    // Mock data for now
    const mockSuppliers = suppliers.data || [
        {
            id: 1,
            name: "Fresh Foods Wholesale",
            contact_person: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            address: "123 Wholesale Ave, Food District, NY 10001",
            created_at: "2024-01-15T10:30:00Z",
            purchases_count: 12,
            payments_count: 8,
            total_purchases: 15750.00,
            total_paid: 12500.00,
            outstanding_balance: 3250.00
        },
        {
            id: 2,
            name: "Premium Meat Suppliers",
            contact_person: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            address: "456 Meat Market St, Industrial Zone, NY 10002",
            created_at: "2024-01-10T14:20:00Z",
            purchases_count: 8,
            payments_count: 8,
            total_purchases: 8900.00,
            total_paid: 8900.00,
            outstanding_balance: 0.00
        },
        {
            id: 3,
            name: "Dairy & Produce Co.",
            contact_person: "Mike Chen",
            email: "<EMAIL>",
            phone: "+****************",
            address: "789 Fresh Market Rd, Agricultural Area, NY 10003",
            created_at: "2024-01-05T09:15:00Z",
            purchases_count: 15,
            payments_count: 10,
            total_purchases: 22300.00,
            total_paid: 18750.00,
            outstanding_balance: 3550.00
        },
        {
            id: 4,
            name: "Equipment & Supplies Ltd",
            contact_person: "Lisa Rodriguez",
            email: "<EMAIL>",
            phone: "+****************",
            address: "321 Equipment Blvd, Commercial District, NY 10004",
            created_at: "2023-12-20T16:45:00Z",
            purchases_count: 6,
            payments_count: 4,
            total_purchases: 12800.00,
            total_paid: 9600.00,
            outstanding_balance: 3200.00
        }
    ];

    const getBalanceStatus = (balance) => {
        if (balance === 0) {
            return { status: 'Paid', color: 'bg-green-100 text-green-800' };
        } else if (balance > 0) {
            return { status: 'Outstanding', color: 'bg-red-100 text-red-800' };
        }
        return { status: 'Credit', color: 'bg-blue-100 text-blue-800' };
    };

    const totalSuppliers = mockSuppliers.length;
    const totalOutstanding = mockSuppliers.reduce((sum, supplier) => sum + supplier.outstanding_balance, 0);
    const totalPurchases = mockSuppliers.reduce((sum, supplier) => sum + supplier.total_purchases, 0);

    return (
        <CaterProLayout>
            <Head title="Suppliers" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Building2 className="h-6 w-6 mr-3 text-primary" />
                            Suppliers
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your supplier relationships and track payments</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('suppliers.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Supplier
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Suppliers</p>
                                    <p className="text-2xl font-bold">{totalSuppliers}</p>
                                </div>
                                <Building2 className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                                    <p className="text-2xl font-bold">${totalPurchases.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Outstanding Balance</p>
                                    <p className="text-2xl font-bold text-red-600">${totalOutstanding.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-red-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Suppliers</CardTitle>
                        <CardDescription>Find suppliers by name, contact person, or email</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search suppliers..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Suppliers Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>All Suppliers ({mockSuppliers.length})</CardTitle>
                        <CardDescription>Manage your supplier database and track payment status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Supplier</th>
                                        <th className="text-left p-3 font-medium">Contact</th>
                                        <th className="text-left p-3 font-medium">Purchases</th>
                                        <th className="text-left p-3 font-medium">Outstanding</th>
                                        <th className="text-left p-3 font-medium">Status</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockSuppliers.map((supplier) => {
                                        const balanceStatus = getBalanceStatus(supplier.outstanding_balance);
                                        return (
                                            <tr key={supplier.id} className="border-b hover:bg-muted/50">
                                                <td className="p-3">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                            <Building2 className="h-5 w-5 text-primary" />
                                                        </div>
                                                        <div>
                                                            <div className="font-medium">{supplier.name}</div>
                                                            {supplier.contact_person && (
                                                                <div className="text-sm text-muted-foreground">
                                                                    Contact: {supplier.contact_person}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <div className="space-y-1">
                                                        <div className="flex items-center text-sm">
                                                            <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                                                            {supplier.email}
                                                        </div>
                                                        {supplier.phone && (
                                                            <div className="flex items-center text-sm text-muted-foreground">
                                                                <Phone className="h-4 w-4 mr-2" />
                                                                {supplier.phone}
                                                            </div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <div>
                                                        <div className="font-medium">{supplier.purchases_count} orders</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            ${supplier.total_purchases.toFixed(2)} total
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-3">
                                                    <span className={`font-semibold ${supplier.outstanding_balance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                                                        ${supplier.outstanding_balance.toFixed(2)}
                                                    </span>
                                                </td>
                                                <td className="p-3">
                                                    <Badge className={balanceStatus.color}>
                                                        {balanceStatus.status}
                                                    </Badge>
                                                </td>
                                                <td className="p-3">
                                                    <div className="flex gap-2">
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('suppliers.show', supplier.id)}>
                                                                View
                                                            </Link>
                                                        </Button>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('suppliers.edit', supplier.id)}>
                                                                Edit
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
