import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { But<PERSON> } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { 
    BarChart3, 
    TrendingUp, 
    DollarSign, 
    ShoppingCart, 
    Package, 
    Users,
    Calendar,
    Download,
    Filter,
    Eye
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';

export default function Index({ auth }) {
    // Mock data for reports
    const reportData = {
        revenue: {
            total: 15750.00,
            thisMonth: 4250.00,
            lastMonth: 3800.00,
            growth: 11.8
        },
        orders: {
            total: 45,
            thisMonth: 12,
            lastMonth: 10,
            growth: 20.0
        },
        inventory: {
            totalItems: 156,
            lowStock: 8,
            outOfStock: 2
        },
        customers: {
            total: 89,
            new: 7,
            returning: 38
        }
    };

    const quickReports = [
        {
            title: "Sales Report",
            description: "Revenue and sales performance analysis",
            icon: DollarSign,
            color: "text-green-600",
            bgColor: "bg-green-100",
            period: "Last 30 days"
        },
        {
            title: "Order Analysis",
            description: "Order trends and customer behavior",
            icon: ShoppingCart,
            color: "text-blue-600",
            bgColor: "bg-blue-100",
            period: "Last 30 days"
        },
        {
            title: "Inventory Report",
            description: "Stock levels and inventory turnover",
            icon: Package,
            color: "text-orange-600",
            bgColor: "bg-orange-100",
            period: "Current"
        },
        {
            title: "Customer Report",
            description: "Customer acquisition and retention",
            icon: Users,
            color: "text-purple-600",
            bgColor: "bg-purple-100",
            period: "Last 30 days"
        }
    ];

    const topSellingItems = [
        { name: "Wedding Package", orders: 8, revenue: 6400 },
        { name: "Corporate Lunch", orders: 15, revenue: 3750 },
        { name: "Birthday Party Package", orders: 12, revenue: 2400 },
        { name: "Anniversary Dinner", orders: 6, revenue: 1800 }
    ];

    return (
        <CaterProLayout>
            <Head title="Reports & Analytics" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <BarChart3 className="h-6 w-6 mr-3 text-primary" />
                            Reports & Analytics
                        </h1>
                        <p className="text-muted-foreground mt-2">Insights and analytics for your catering business</p>
                    </div>
                    <div className="flex gap-3">
                        <Select defaultValue="30days">
                            <SelectTrigger className="w-40">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="7days">Last 7 days</SelectItem>
                                <SelectItem value="30days">Last 30 days</SelectItem>
                                <SelectItem value="90days">Last 90 days</SelectItem>
                                <SelectItem value="year">This year</SelectItem>
                            </SelectContent>
                        </Select>
                        <Button variant="outline">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                                    <p className="text-2xl font-bold">${reportData.revenue.total.toFixed(2)}</p>
                                    <div className="flex items-center mt-1">
                                        <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                                        <span className="text-sm text-green-600">+{reportData.revenue.growth}%</span>
                                    </div>
                                </div>
                                <DollarSign className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                                    <p className="text-2xl font-bold">{reportData.orders.total}</p>
                                    <div className="flex items-center mt-1">
                                        <TrendingUp className="h-4 w-4 text-blue-600 mr-1" />
                                        <span className="text-sm text-blue-600">+{reportData.orders.growth}%</span>
                                    </div>
                                </div>
                                <ShoppingCart className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Inventory Items</p>
                                    <p className="text-2xl font-bold">{reportData.inventory.totalItems}</p>
                                    <div className="flex items-center mt-1">
                                        <span className="text-sm text-orange-600">{reportData.inventory.lowStock} low stock</span>
                                    </div>
                                </div>
                                <Package className="h-8 w-8 text-orange-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                                    <p className="text-2xl font-bold">{reportData.customers.total}</p>
                                    <div className="flex items-center mt-1">
                                        <span className="text-sm text-purple-600">{reportData.customers.new} new this month</span>
                                    </div>
                                </div>
                                <Users className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Reports */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Reports</CardTitle>
                        <CardDescription>Generate detailed reports for different aspects of your business</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {quickReports.map((report, index) => (
                                <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center">
                                            <div className={`h-10 w-10 rounded-lg ${report.bgColor} flex items-center justify-center mr-3`}>
                                                <report.icon className={`h-5 w-5 ${report.color}`} />
                                            </div>
                                            <div>
                                                <h3 className="font-medium">{report.title}</h3>
                                                <p className="text-sm text-muted-foreground">{report.description}</p>
                                                <p className="text-xs text-muted-foreground mt-1">{report.period}</p>
                                            </div>
                                        </div>
                                        <div className="flex gap-2">
                                            <Button variant="outline" size="sm">
                                                <Eye className="h-4 w-4 mr-1" />
                                                View
                                            </Button>
                                            <Button variant="outline" size="sm">
                                                <Download className="h-4 w-4 mr-1" />
                                                Export
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Top Selling Items */}
                <Card>
                    <CardHeader>
                        <CardTitle>Top Selling Items</CardTitle>
                        <CardDescription>Most popular items in the last 30 days</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {topSellingItems.map((item, index) => (
                                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                    <div className="flex items-center">
                                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                                            <span className="text-sm font-medium text-primary">{index + 1}</span>
                                        </div>
                                        <div>
                                            <p className="font-medium">{item.name}</p>
                                            <p className="text-sm text-muted-foreground">{item.orders} orders</p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-semibold">${item.revenue.toFixed(2)}</p>
                                        <p className="text-sm text-muted-foreground">revenue</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
