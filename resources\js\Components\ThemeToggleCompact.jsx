import React from 'react';
import { Button } from '@/Components/ui/button';
import { Sun, Moon, Monitor } from 'lucide-react';
import { useTheme } from '@/Contexts/ThemeContext';

export function ThemeToggleCompact() {
    const { theme, setTheme } = useTheme();

    const themes = [
        {
            name: 'light',
            icon: Sun,
            label: 'Light'
        },
        {
            name: 'dark',
            icon: Moon,
            label: 'Dark'
        },
        {
            name: 'system',
            icon: Monitor,
            label: 'System'
        }
    ];

    return (
        <div className="flex items-center rounded-md border bg-background p-0.5">
            {themes.map((themeOption) => {
                const Icon = themeOption.icon;
                const isActive = theme === themeOption.name;
                
                return (
                    <Button
                        key={themeOption.name}
                        variant={isActive ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setTheme(themeOption.name)}
                        className={`h-6 w-6 p-0 transition-all duration-200 ${
                            isActive 
                                ? 'bg-primary text-primary-foreground shadow-sm' 
                                : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                        }`}
                        title={`Switch to ${themeOption.label} theme`}
                    >
                        <Icon className="h-3 w-3" />
                        <span className="sr-only">{themeOption.label}</span>
                    </Button>
                );
            })}
        </div>
    );
}
