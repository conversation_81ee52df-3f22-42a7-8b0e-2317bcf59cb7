import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { Wrench, ArrowLeft, Save, DollarSign, Tag } from 'lucide-react';

export default function Create({ auth }) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        price: '',
        price_type: 'flat_rate',
        category: '',
        status: 'active',
    });

    const submit = (e) => {
        e.preventDefault();
        post(route('services.store'));
    };

    return (
        <CaterProLayout>
            <Head title="Add Service" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Wrench className="h-6 w-6 mr-3 text-primary" />
                            Add Service
                        </h1>
                        <p className="text-muted-foreground mt-2">Create a new service offering</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('services.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Services
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Service Information</CardTitle>
                            <CardDescription>Enter the basic details for the service</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Service Name *</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Full Service Catering"
                                        required
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="category">Category *</Label>
                                    <div className="relative">
                                        <Tag className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="category"
                                            type="text"
                                            value={data.category}
                                            onChange={(e) => setData('category', e.target.value)}
                                            placeholder="e.g., Catering, Setup, Coordination"
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.category} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description *</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Describe what this service includes..."
                                    rows={4}
                                    required
                                />
                                <InputError message={errors.description} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Pricing & Status</CardTitle>
                            <CardDescription>Set pricing structure and availability</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="price">Price *</Label>
                                    <div className="relative">
                                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="price"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            value={data.price}
                                            onChange={(e) => setData('price', e.target.value)}
                                            placeholder="0.00"
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.price} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="price_type">Price Type *</Label>
                                    <Select value={data.price_type} onValueChange={(value) => setData('price_type', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select price type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="flat_rate">Flat Rate</SelectItem>
                                            <SelectItem value="per_person">Per Person</SelectItem>
                                            <SelectItem value="per_hour">Per Hour</SelectItem>
                                            <SelectItem value="per_day">Per Day</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.price_type} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status *</Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="inactive">Inactive</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.status} />
                                </div>
                            </div>

                            <div className="bg-muted/50 p-4 rounded-lg">
                                <h4 className="font-medium mb-2">Price Preview</h4>
                                <p className="text-lg font-semibold text-primary">
                                    {data.price ? `$${data.price}` : '$0.00'}
                                    {data.price_type === 'per_person' && '/person'}
                                    {data.price_type === 'per_hour' && '/hour'}
                                    {data.price_type === 'per_day' && '/day'}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('services.index')}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Creating...' : 'Create Service'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
