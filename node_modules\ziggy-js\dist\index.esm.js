import{parse as r,stringify as t}from"qs";function n(r,t){for(var n=0;n<t.length;n++){var e=t[n];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(r,a(e.key),e)}}function e(r,t,e){return t&&n(r.prototype,t),e&&n(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function i(){return i=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)({}).hasOwnProperty.call(n,e)&&(r[e]=n[e])}return r},i.apply(null,arguments)}function u(r){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},u(r)}function o(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){}))}catch(r){}return(o=function(){return!!r})()}function f(r,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,t){return r.__proto__=t,r},f(r,t)}function a(r){var t=function(r){if("object"!=typeof r||!r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var n=t.call(r,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}(r);return"symbol"==typeof t?t:t+""}function c(r){var t="function"==typeof Map?new Map:void 0;return c=function(r){if(null===r||!function(r){try{return-1!==Function.toString.call(r).indexOf("[native code]")}catch(t){return"function"==typeof r}}(r))return r;if("function"!=typeof r)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(r))return t.get(r);t.set(r,n)}function n(){return function(r,t,n){if(o())return Reflect.construct.apply(null,arguments);var e=[null];e.push.apply(e,t);var i=new(r.bind.apply(r,e));return n&&f(i,n.prototype),i}(r,arguments,u(this).constructor)}return n.prototype=Object.create(r.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),f(n,r)},c(r)}var s=/*#__PURE__*/function(){function t(r,t,n){var e,i;this.name=r,this.definition=t,this.bindings=null!=(e=t.bindings)?e:{},this.wheres=null!=(i=t.wheres)?i:{},this.config=n}var n=t.prototype;return n.matchesUrl=function(t){var n,e=this;if(!this.definition.methods.includes("GET"))return!1;var i=this.template.replace(/[.*+$()[\]]/g,"\\$&").replace(/(\/?){([^}?]*)(\??)}/g,function(r,t,n,i){var u,o="(?<"+n+">"+((null==(u=e.wheres[n])?void 0:u.replace(/(^\^)|(\$$)/g,""))||"[^/?]+")+")";return i?"("+t+o+")?":""+t+o}).replace(/^\w+:\/\//,""),u=t.replace(/^\w+:\/\//,"").split("?"),o=u[0],f=u[1],a=null!=(n=new RegExp("^"+i+"/?$").exec(o))?n:new RegExp("^"+i+"/?$").exec(decodeURI(o));if(a){for(var c in a.groups)a.groups[c]="string"==typeof a.groups[c]?decodeURIComponent(a.groups[c]):a.groups[c];return{params:a.groups,query:r(f)}}return!1},n.compile=function(r){var t=this;return this.parameterSegments.length?this.template.replace(/{([^}?]+)(\??)}/g,function(n,e,i){var u,o;if(!i&&[null,void 0].includes(r[e]))throw new Error("Ziggy error: '"+e+"' parameter is required for route '"+t.name+"'.");if(t.wheres[e]&&!new RegExp("^"+(i?"("+t.wheres[e]+")?":t.wheres[e])+"$").test(null!=(o=r[e])?o:""))throw new Error("Ziggy error: '"+e+"' parameter '"+r[e]+"' does not match required format '"+t.wheres[e]+"' for route '"+t.name+"'.");return encodeURI(null!=(u=r[e])?u:"").replace(/%7C/g,"|").replace(/%25/g,"%").replace(/\$/g,"%24")}).replace(this.config.absolute?/(\.[^/]+?)(\/\/)/:/(^)(\/\/)/,"$1/").replace(/\/+$/,""):this.template},e(t,[{key:"template",get:function(){var r=(this.origin+"/"+this.definition.uri).replace(/\/+$/,"");return""===r?"/":r}},{key:"origin",get:function(){return this.config.absolute?this.definition.domain?""+this.config.url.match(/^\w+:\/\//)[0]+this.definition.domain+(this.config.port?":"+this.config.port:""):this.config.url:""}},{key:"parameterSegments",get:function(){var r,t;return null!=(r=null==(t=this.template.match(/{[^}?]+\??}/g))?void 0:t.map(function(r){return{name:r.replace(/{|\??}/g,""),required:!/\?}$/.test(r)}}))?r:[]}}])}(),l=/*#__PURE__*/function(r){function n(t,n,e,u){var o;if(void 0===e&&(e=!0),(o=r.call(this)||this).t=null!=u?u:"undefined"!=typeof Ziggy?Ziggy:null==globalThis?void 0:globalThis.Ziggy,o.t=i({},o.t,{absolute:e}),t){if(!o.t.routes[t])throw new Error("Ziggy error: route '"+t+"' is not in the route list.");o.i=new s(t,o.t.routes[t],o.t),o.u=o.o(n)}return o}var u,o;o=r,(u=n).prototype=Object.create(o.prototype),u.prototype.constructor=u,f(u,o);var a=n.prototype;return a.toString=function(){var r=this,n=Object.keys(this.u).filter(function(t){return!r.i.parameterSegments.some(function(r){return r.name===t})}).filter(function(r){return"_query"!==r}).reduce(function(t,n){var e;return i({},t,((e={})[n]=r.u[n],e))},{});return this.i.compile(this.u)+t(i({},n,this.u._query),{addQueryPrefix:!0,arrayFormat:"indices",encodeValuesOnly:!0,skipNulls:!0,encoder:function(r,t){return"boolean"==typeof r?Number(r):t(r)}})},a.l=function(r){var t=this;r?this.t.absolute&&r.startsWith("/")&&(r=this.v().host+r):r=this.h();var n={},e=Object.entries(this.t.routes).find(function(e){return n=new s(e[0],e[1],t.t).matchesUrl(r)})||[void 0,void 0];return i({name:e[0]},n,{route:e[1]})},a.h=function(){var r=this.v(),t=r.pathname,n=r.search;return(this.t.absolute?r.host+t:t.replace(this.t.url.replace(/^\w*:\/\/[^/]+/,""),"").replace(/^\/+/,"/"))+n},a.current=function(r,t){var n=this.l(),e=n.name,u=n.params,o=n.query,f=n.route;if(!r)return e;var a=new RegExp("^"+r.replace(/\./g,"\\.").replace(/\*/g,".*")+"$").test(e);if([null,void 0].includes(t)||!a)return a;var c=new s(e,f,this.t);t=this.o(t,c);var l=i({},u,o);if(Object.values(t).every(function(r){return!r})&&!Object.values(l).some(function(r){return void 0!==r}))return!0;var v=function(r,t){return Object.entries(r).every(function(r){var n=r[0],e=r[1];return Array.isArray(e)&&Array.isArray(t[n])?e.every(function(r){return t[n].includes(r)}):"object"==typeof e&&"object"==typeof t[n]&&null!==e&&null!==t[n]?v(e,t[n]):t[n]==e})};return v(t,l)},a.v=function(){var r,t,n,e,i,u,o="undefined"!=typeof window?window.location:{},f=o.host,a=o.pathname,c=o.search;return{host:null!=(r=null==(t=this.t.location)?void 0:t.host)?r:void 0===f?"":f,pathname:null!=(n=null==(e=this.t.location)?void 0:e.pathname)?n:void 0===a?"":a,search:null!=(i=null==(u=this.t.location)?void 0:u.search)?i:void 0===c?"":c}},a.has=function(r){return this.t.routes.hasOwnProperty(r)},a.o=function(r,t){var n=this;void 0===r&&(r={}),void 0===t&&(t=this.i),null!=r||(r={}),r=["string","number"].includes(typeof r)?[r]:r;var e=t.parameterSegments.filter(function(r){return!n.t.defaults[r.name]});if(Array.isArray(r))r=r.reduce(function(r,t,n){var u,o;return i({},r,e[n]?((u={})[e[n].name]=t,u):"object"==typeof t?t:((o={})[t]="",o))},{});else if(1===e.length&&!r[e[0].name]&&(r.hasOwnProperty(Object.values(t.bindings)[0])||r.hasOwnProperty("id"))){var u;(u={})[e[0].name]=r,r=u}return i({},this.p(t),this.m(r,t))},a.p=function(r){var t=this;return r.parameterSegments.filter(function(r){return t.t.defaults[r.name]}).reduce(function(r,n,e){var u,o=n.name;return i({},r,((u={})[o]=t.t.defaults[o],u))},{})},a.m=function(r,t){var n=t.bindings,e=t.parameterSegments;return Object.entries(r).reduce(function(r,t){var u,o,f=t[0],a=t[1];if(!a||"object"!=typeof a||Array.isArray(a)||!e.some(function(r){return r.name===f}))return i({},r,((o={})[f]=a,o));if(!a.hasOwnProperty(n[f])){if(!a.hasOwnProperty("id"))throw new Error("Ziggy error: object passed as '"+f+"' parameter is missing route model binding key '"+n[f]+"'.");n[f]="id"}return i({},r,((u={})[f]=a[n[f]],u))},{})},a.valueOf=function(){return this.toString()},e(n,[{key:"params",get:function(){var r=this.l();return i({},r.params,r.query)}},{key:"routeParams",get:function(){return this.l().params}},{key:"queryParams",get:function(){return this.l().query}}])}(/*#__PURE__*/c(String));function v(r,t,n,e){var i=new l(r,t,n,e);return r?i.toString():i}var h={install:function(r,t){var n=function(r,n,e,i){return void 0===i&&(i=t),v(r,n,e,i)};parseInt(r.version)>2?(r.config.globalProperties.route=n,r.provide("route",n)):r.mixin({methods:{route:n}})}};function g(r){if(!r&&!globalThis.Ziggy&&"undefined"==typeof Ziggy)throw new Error("Ziggy error: missing configuration. Ensure that a `Ziggy` variable is defined globally or pass a config object into the useRoute hook.");return function(t,n,e,i){return void 0===i&&(i=r),v(t,n,e,i)}}export{h as ZiggyVue,v as route,g as useRoute};
