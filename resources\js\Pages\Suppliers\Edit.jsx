import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import InputError from '@/Components/InputError';
import { Building2, ArrowLeft, Save, Mail, Phone, MapPin, User } from 'lucide-react';

export default function Edit({ auth, supplier }) {
    // Mock data if not provided
    const mockSupplier = supplier || {
        id: 1,
        name: "Fresh Foods Wholesale",
        contact_person: "<PERSON>",
        email: "<EMAIL>",
        phone: "+****************",
        address: "123 Wholesale Ave, Food District, NY 10001"
    };

    const { data, setData, put, processing, errors } = useForm({
        name: mockSupplier.name || '',
        contact_person: mockSupplier.contact_person || '',
        email: mockSupplier.email || '',
        phone: mockSupplier.phone || '',
        address: mockSupplier.address || '',
    });

    const submit = (e) => {
        e.preventDefault();
        put(route('suppliers.update', mockSupplier.id));
    };

    return (
        <CaterProLayout>
            <Head title={`Edit Supplier: ${mockSupplier.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Building2 className="h-6 w-6 mr-3 text-primary" />
                            Edit Supplier
                        </h1>
                        <p className="text-muted-foreground mt-2">Update supplier information and contact details</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('suppliers.show', mockSupplier.id)}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Supplier
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Supplier Information</CardTitle>
                            <CardDescription>Update the supplier's basic information and contact details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Company Name *</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Fresh Foods Wholesale"
                                        required
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_person">Contact Person</Label>
                                    <div className="relative">
                                        <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="contact_person"
                                            type="text"
                                            value={data.contact_person}
                                            onChange={(e) => setData('contact_person', e.target.value)}
                                            placeholder="e.g., John Smith"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.contact_person} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email Address</Label>
                                    <div className="relative">
                                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="e.g., <EMAIL>"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.email} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <div className="relative">
                                        <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="phone"
                                            type="tel"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="e.g., +****************"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.phone} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Business Address</Label>
                                <div className="relative">
                                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        placeholder="Enter full business address..."
                                        className="pl-10"
                                        rows={3}
                                    />
                                </div>
                                <InputError message={errors.address} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Update History</CardTitle>
                            <CardDescription>Track changes to supplier information</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-sm text-muted-foreground">
                                <p>Last updated: {new Date().toLocaleDateString()}</p>
                                <p className="mt-1">
                                    Changes will be saved immediately and reflected across all related purchases and payments.
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('suppliers.show', mockSupplier.id)}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Updating...' : 'Update Supplier'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
