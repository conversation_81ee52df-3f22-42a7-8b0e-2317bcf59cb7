import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { Truck, ArrowLeft, Edit, Trash2, DollarSign, Hash, Calendar } from 'lucide-react';

export default function Show({ auth, rentalItem }) {
    // Mock data if not provided
    const item = rentalItem || {
        id: 1,
        name: "Round Tables (8-person)",
        sku: "TBL-R8-001",
        description: "High-quality round tables perfect for events and gatherings. Made from durable materials with elegant finish.",
        quantity: 15,
        daily_rate: 25.00,
        status: "available",
        created_at: "2024-01-15T10:30:00Z",
        updated_at: "2024-01-20T14:45:00Z"
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'available':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'rented':
                return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
            case 'maintenance':
                return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    return (
        <CaterProLayout>
            <Head title={`Rental Item: ${item.name}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Truck className="h-6 w-6 mr-3 text-primary" />
                            {item.name}
                        </h1>
                        <p className="text-muted-foreground mt-2">Rental item details and information</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('rentals.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Rentals
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('rentals.edit', item.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Information */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Item Information</CardTitle>
                                <CardDescription>Basic details about this rental item</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Item Name</h3>
                                        <p className="text-lg font-semibold">{item.name}</p>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Hash className="h-4 w-4 mr-1" />
                                            SKU
                                        </h3>
                                        <p className="text-lg">{item.sku || 'Not set'}</p>
                                    </div>
                                </div>

                                <Separator />

                                <div>
                                    <h3 className="font-medium text-sm text-muted-foreground mb-2">Description</h3>
                                    <p className="text-foreground">{item.description || 'No description provided'}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Rental History</CardTitle>
                                <CardDescription>Recent rental transactions for this item</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center py-8 text-muted-foreground">
                                    <Truck className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>No rental history available</p>
                                    <p className="text-sm">This item hasn't been rented yet</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Inventory Status</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Status</span>
                                    <Badge className={getStatusBadge(item.status)}>
                                        {item.status}
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Quantity</span>
                                    <span className="text-lg font-semibold">{item.quantity} units</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <DollarSign className="h-4 w-4 mr-1" />
                                        Daily Rate
                                    </span>
                                    <span className="text-lg font-semibold">${item.daily_rate}/day</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Record Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Created
                                    </span>
                                    <p className="text-sm">{new Date(item.created_at).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </span>
                                    <p className="text-sm">{new Date(item.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </CaterProLayout>
    );
}
