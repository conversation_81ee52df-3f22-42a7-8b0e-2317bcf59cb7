import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, useForm, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import InputError from '@/Components/InputError';
import { ShoppingBag, ArrowLeft, Save, Calendar, Receipt, Plus, Trash2, DollarSign } from 'lucide-react';
import { useState } from 'react';

export default function Create({ auth, suppliers = [], inventoryItems = [] }) {
    const { data, setData, post, processing, errors } = useForm({
        supplier_id: '',
        purchase_date: new Date().toISOString().split('T')[0],
        notes: '',
        invoice_number: '',
        items: [{ inventory_item_id: '', quantity: '', unit_price: '' }],
    });

    // Mock data if not provided
    const mockSuppliers = suppliers.length > 0 ? suppliers : [
        { id: 1, name: "Fresh Foods Wholesale" },
        { id: 2, name: "Premium Meat Suppliers" },
        { id: 3, name: "Dairy & Produce Co." },
    ];

    const mockInventoryItems = inventoryItems.length > 0 ? inventoryItems : [
        { id: 1, name: "Chicken Breast", unit: { name: "kg" } },
        { id: 2, name: "Basmati Rice", unit: { name: "kg" } },
        { id: 3, name: "Fresh Tomatoes", unit: { name: "kg" } },
        { id: 4, name: "Onions", unit: { name: "kg" } },
        { id: 5, name: "Cooking Oil", unit: { name: "liters" } },
    ];

    const addItem = () => {
        setData('items', [...data.items, { inventory_item_id: '', quantity: '', unit_price: '' }]);
    };

    const removeItem = (index) => {
        const newItems = data.items.filter((_, i) => i !== index);
        setData('items', newItems);
    };

    const updateItem = (index, field, value) => {
        const newItems = [...data.items];
        newItems[index][field] = value;
        setData('items', newItems);
    };

    const calculateTotal = () => {
        return data.items.reduce((total, item) => {
            const quantity = parseFloat(item.quantity) || 0;
            const unitPrice = parseFloat(item.unit_price) || 0;
            return total + (quantity * unitPrice);
        }, 0);
    };

    const submit = (e) => {
        e.preventDefault();
        post(route('purchases.store'));
    };

    return (
        <CaterProLayout>
            <Head title="Create Purchase" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <ShoppingBag className="h-6 w-6 mr-3 text-primary" />
                            Create Purchase
                        </h1>
                        <p className="text-muted-foreground mt-2">Record a new inventory purchase from supplier</p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('purchases.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Purchases
                        </Link>
                    </Button>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Purchase Information</CardTitle>
                            <CardDescription>Enter the basic purchase details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="supplier_id">Supplier *</Label>
                                    <Select value={data.supplier_id} onValueChange={(value) => setData('supplier_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select supplier" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {mockSuppliers.map((supplier) => (
                                                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.supplier_id} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="purchase_date">Purchase Date *</Label>
                                    <div className="relative">
                                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="purchase_date"
                                            type="date"
                                            value={data.purchase_date}
                                            onChange={(e) => setData('purchase_date', e.target.value)}
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    <InputError message={errors.purchase_date} />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-2">
                                    <Label htmlFor="invoice_number">Invoice Number</Label>
                                    <div className="relative">
                                        <Receipt className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="invoice_number"
                                            type="text"
                                            value={data.invoice_number}
                                            onChange={(e) => setData('invoice_number', e.target.value)}
                                            placeholder="e.g., INV-2024-001"
                                            className="pl-10"
                                        />
                                    </div>
                                    <InputError message={errors.invoice_number} />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes</Label>
                                <Textarea
                                    id="notes"
                                    value={data.notes}
                                    onChange={(e) => setData('notes', e.target.value)}
                                    placeholder="Any additional notes about this purchase..."
                                    rows={3}
                                />
                                <InputError message={errors.notes} />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Purchase Items</CardTitle>
                                    <CardDescription>Add items to this purchase</CardDescription>
                                </div>
                                <Button type="button" variant="outline" onClick={addItem}>
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Item
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {data.items.map((item, index) => (
                                <div key={index} className="p-4 border rounded-lg space-y-4">
                                    <div className="flex items-center justify-between">
                                        <h4 className="font-medium">Item {index + 1}</h4>
                                        {data.items.length > 1 && (
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeItem(index)}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        )}
                                    </div>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <div className="md:col-span-2 space-y-2">
                                            <Label>Inventory Item *</Label>
                                            <Select 
                                                value={item.inventory_item_id} 
                                                onValueChange={(value) => updateItem(index, 'inventory_item_id', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select item" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {mockInventoryItems.map((inventoryItem) => (
                                                        <SelectItem key={inventoryItem.id} value={inventoryItem.id.toString()}>
                                                            {inventoryItem.name} ({inventoryItem.unit?.name})
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors[`items.${index}.inventory_item_id`]} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label>Quantity *</Label>
                                            <Input
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                value={item.quantity}
                                                onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                                                placeholder="0"
                                                required
                                            />
                                            <InputError message={errors[`items.${index}.quantity`]} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label>Unit Price *</Label>
                                            <div className="relative">
                                                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                                <Input
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    value={item.unit_price}
                                                    onChange={(e) => updateItem(index, 'unit_price', e.target.value)}
                                                    placeholder="0.00"
                                                    className="pl-10"
                                                    required
                                                />
                                            </div>
                                            <InputError message={errors[`items.${index}.unit_price`]} />
                                        </div>
                                    </div>

                                    {item.quantity && item.unit_price && (
                                        <div className="text-right">
                                            <span className="text-sm text-muted-foreground">Subtotal: </span>
                                            <span className="font-semibold">
                                                ${(parseFloat(item.quantity) * parseFloat(item.unit_price)).toFixed(2)}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            ))}

                            <Separator />

                            <div className="flex justify-between items-center">
                                <span className="text-lg font-medium">Total Amount:</span>
                                <span className="text-2xl font-bold text-primary">
                                    ${calculateTotal().toFixed(2)}
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('purchases.index')}>
                                Cancel
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing || data.items.length === 0}>
                            <Save className="h-4 w-4 mr-2" />
                            {processing ? 'Creating...' : 'Create Purchase'}
                        </Button>
                    </div>
                </form>
            </div>
        </CaterProLayout>
    );
}
