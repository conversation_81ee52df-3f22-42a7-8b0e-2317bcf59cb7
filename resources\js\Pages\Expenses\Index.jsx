import CaterProLayout from '@/Layouts/CaterProLayout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { DollarSign, Plus, Search, Filter, Calendar, Receipt } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, expenses = { data: [], links: [] } }) {
    // Mock data for now
    const mockExpenses = expenses.data || [
        {
            id: 1,
            title: "Food Supplies - Wholesale Market",
            description: "Weekly food supplies purchase for catering events",
            amount: 1250.00,
            category: "Food & Ingredients",
            expense_date: "2024-01-20",
            receipt_number: "RCP-001-2024"
        },
        {
            id: 2,
            title: "Equipment Maintenance",
            description: "Repair and maintenance of kitchen equipment",
            amount: 350.00,
            category: "Equipment",
            expense_date: "2024-01-18",
            receipt_number: "RCP-002-2024"
        },
        {
            id: 3,
            title: "Vehicle Fuel",
            description: "Fuel for delivery vehicles",
            amount: 120.00,
            category: "Transportation",
            expense_date: "2024-01-17",
            receipt_number: "RCP-003-2024"
        },
        {
            id: 4,
            title: "Marketing Materials",
            description: "Brochures and business cards printing",
            amount: 85.00,
            category: "Marketing",
            expense_date: "2024-01-15",
            receipt_number: "RCP-004-2024"
        }
    ];

    const getCategoryColor = (category) => {
        const colors = {
            'Food & Ingredients': 'bg-green-100 text-green-800',
            'Equipment': 'bg-blue-100 text-blue-800',
            'Transportation': 'bg-yellow-100 text-yellow-800',
            'Marketing': 'bg-purple-100 text-purple-800',
            'Utilities': 'bg-orange-100 text-orange-800',
            'Other': 'bg-gray-100 text-gray-800'
        };
        return colors[category] || 'bg-gray-100 text-gray-800';
    };

    const totalExpenses = mockExpenses.reduce((sum, expense) => sum + expense.amount, 0);

    return (
        <CaterProLayout>
            <Head title="Expenses" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <DollarSign className="h-6 w-6 mr-3 text-primary" />
                            Expenses
                        </h1>
                        <p className="text-muted-foreground mt-2">Track and manage your business expenses</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('expenses.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Expense
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
                                    <p className="text-2xl font-bold">${totalExpenses.toFixed(2)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">This Month</p>
                                    <p className="text-2xl font-bold">${totalExpenses.toFixed(2)}</p>
                                </div>
                                <Calendar className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Records</p>
                                    <p className="text-2xl font-bold">{mockExpenses.length}</p>
                                </div>
                                <Receipt className="h-8 w-8 text-primary" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Expenses</CardTitle>
                        <CardDescription>Find expenses by title, category, or receipt number</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search expenses..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Expenses Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Expenses ({mockExpenses.length})</CardTitle>
                        <CardDescription>All your business expenses</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">Expense</th>
                                        <th className="text-left p-3 font-medium">Category</th>
                                        <th className="text-left p-3 font-medium">Amount</th>
                                        <th className="text-left p-3 font-medium">Date</th>
                                        <th className="text-left p-3 font-medium">Receipt</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {mockExpenses.map((expense) => (
                                        <tr key={expense.id} className="border-b hover:bg-muted/50">
                                            <td className="p-3">
                                                <div className="flex items-center">
                                                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                                        <DollarSign className="h-5 w-5 text-primary" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{expense.title}</div>
                                                        <div className="text-sm text-muted-foreground line-clamp-1">
                                                            {expense.description}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="p-3">
                                                <Badge className={getCategoryColor(expense.category)}>
                                                    {expense.category}
                                                </Badge>
                                            </td>
                                            <td className="p-3">
                                                <span className="font-semibold">${expense.amount.toFixed(2)}</span>
                                            </td>
                                            <td className="p-3 text-muted-foreground">
                                                {new Date(expense.expense_date).toLocaleDateString()}
                                            </td>
                                            <td className="p-3 text-muted-foreground">
                                                {expense.receipt_number || 'N/A'}
                                            </td>
                                            <td className="p-3">
                                                <div className="flex gap-2">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('expenses.show', expense.id)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('expenses.edit', expense.id)}>
                                                            Edit
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </CaterProLayout>
    );
}
