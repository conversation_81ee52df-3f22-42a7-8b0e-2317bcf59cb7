import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, <PERSON> } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Wrench, Plus, Search, Filter } from 'lucide-react';
import { Input } from '@/Components/ui/input';

export default function Index({ auth, services = { data: [], links: [] } }) {
    // Mock data for now
    const mockServices = services.data || [
        {
            id: 1,
            name: "Full Service Catering",
            description: "Complete catering service including setup, service, and cleanup",
            price: 45.00,
            price_type: "per_person",
            category: "Catering",
            status: "active"
        },
        {
            id: 2,
            name: "Event Setup & Breakdown",
            description: "Professional setup and breakdown of event space",
            price: 200.00,
            price_type: "flat_rate",
            category: "Setup",
            status: "active"
        },
        {
            id: 3,
            name: "Bartending Service",
            description: "Professional bartender for your event",
            price: 35.00,
            price_type: "per_hour",
            category: "Beverage",
            status: "active"
        },
        {
            id: 4,
            name: "Wedding Coordination",
            description: "Full wedding day coordination and management",
            price: 800.00,
            price_type: "flat_rate",
            category: "Coordination",
            status: "inactive"
        }
    ];

    const getStatusBadge = (status) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800 hover:bg-green-100';
            case 'inactive':
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
            default:
                return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
        }
    };

    const formatPrice = (price, priceType) => {
        const formattedPrice = `$${price}`;
        switch (priceType) {
            case 'per_person':
                return `${formattedPrice}/person`;
            case 'per_hour':
                return `${formattedPrice}/hour`;
            case 'flat_rate':
                return formattedPrice;
            default:
                return formattedPrice;
        }
    };

    return (
        <CaterProLayout>
            <Head title="Services" />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <Wrench className="h-6 w-6 mr-3 text-primary" />
                            Services
                        </h1>
                        <p className="text-muted-foreground mt-2">Manage your catering and event services</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button asChild>
                            <Link href={route('services.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Service
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Services</CardTitle>
                        <CardDescription>Find services by name, category, or status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search services..."
                                    className="w-full"
                                />
                            </div>
                            <Button variant="outline">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Services Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {mockServices.map((service) => (
                        <Card key={service.id} className="hover:shadow-md transition-shadow">
                            <CardHeader>
                                <div className="flex items-start justify-between">
                                    <div className="flex items-center">
                                        <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center mr-3">
                                            <Wrench className="h-5 w-5 text-primary" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{service.name}</CardTitle>
                                            <p className="text-sm text-muted-foreground">{service.category}</p>
                                        </div>
                                    </div>
                                    <Badge className={getStatusBadge(service.status)}>
                                        {service.status}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground mb-4 line-clamp-2">
                                    {service.description}
                                </p>
                                
                                <div className="flex items-center justify-between mb-4">
                                    <span className="text-2xl font-bold text-primary">
                                        {formatPrice(service.price, service.price_type)}
                                    </span>
                                </div>

                                <div className="flex gap-2">
                                    <Button variant="outline" size="sm" asChild className="flex-1">
                                        <Link href={route('services.show', service.id)}>
                                            View
                                        </Link>
                                    </Button>
                                    <Button variant="outline" size="sm" asChild className="flex-1">
                                        <Link href={route('services.edit', service.id)}>
                                            Edit
                                        </Link>
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Empty State */}
                {mockServices.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Wrench className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                            <h3 className="text-lg font-semibold mb-2">No services found</h3>
                            <p className="text-muted-foreground mb-4">
                                Get started by adding your first service
                            </p>
                            <Button asChild>
                                <Link href={route('services.create')}>
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Service
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                )}
            </div>
        </CaterProLayout>
    );
}
