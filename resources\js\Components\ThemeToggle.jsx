import React from 'react';
import { Button } from '@/Components/ui/button';
import { Sun, Moon, Monitor } from 'lucide-react';
import { useTheme } from '@/Contexts/ThemeContext';

export function ThemeToggle() {
    const { theme, setTheme } = useTheme();

    const themes = [
        {
            name: 'light',
            icon: Sun,
            label: 'Light'
        },
        {
            name: 'dark',
            icon: Moon,
            label: 'Dark'
        },
        {
            name: 'system',
            icon: Monitor,
            label: 'System'
        }
    ];

    return (
        <div className="theme-toggle-group">
            {themes.map((themeOption) => {
                const Icon = themeOption.icon;
                const isActive = theme === themeOption.name;

                return (
                    <Button
                        key={themeOption.name}
                        variant="ghost"
                        size="sm"
                        onClick={() => setTheme(themeOption.name)}
                        className={`theme-toggle-button h-7 px-3 ${isActive ? 'active' : ''}`}
                        title={`Switch to ${themeOption.label} theme`}
                    >
                        <Icon className="h-3.5 w-3.5" />
                        <span className="sr-only">{themeOption.label}</span>
                    </Button>
                );
            })}
        </div>
    );
}
