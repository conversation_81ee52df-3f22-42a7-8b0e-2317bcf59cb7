<?php

use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\InventoryItemController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RentalItemController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::resource('inventory', InventoryItemController::class);
    Route::resource('rentals', RentalItemController::class);
    Route::resource('orders', OrderController::class);
    Route::resource('customers', CustomerController::class);
    Route::resource('expenses', ExpenseController::class);
    Route::resource('suppliers', SupplierController::class);
    Route::resource('purchases', PurchaseController::class);
    Route::resource('payments', PaymentController::class);

    // Services routes
    Route::get('/services', function () {
        return Inertia::render('Services/Index');
    })->name('services.index');
    Route::get('/services/create', function () {
        return Inertia::render('Services/Create');
    })->name('services.create');
    Route::get('/services/{id}', function ($id) {
        return Inertia::render('Services/Show', ['service' => ['id' => $id]]);
    })->name('services.show');
    Route::get('/services/{id}/edit', function ($id) {
        return Inertia::render('Services/Edit', ['service' => ['id' => $id]]);
    })->name('services.edit');

    // Reports routes
    Route::get('/reports', function () {
        return Inertia::render('Reports/Index');
    })->name('reports.index');
});

require __DIR__.'/auth.php';
