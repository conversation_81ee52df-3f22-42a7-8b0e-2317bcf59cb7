import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { Package, ArrowLeft, Edit, Trash2, DollarSign, Hash, Calendar, User, Building } from 'lucide-react';

export default function Show({ auth, item }) {
    // Mock data for demonstration if item is not provided
    const mockItem = item || {
        id: 1,
        name: "Chicken Breast",
        sku: "CHK-001",
        description: "Fresh organic chicken breast, perfect for grilling and roasting",
        quantity: 25,
        reorder_level: 10,
        cost_price: 5.99,
        selling_price: 8.99,
        category: { name: "Meat", id: 1 },
        supplier: { name: "Fresh Foods Co.", id: 1 },
        unit: { name: "Pounds", abbreviation: "lbs", id: 1 },
        created_at: "2025-06-15T10:30:00Z",
        updated_at: "2025-06-17T14:20:00Z"
    };

    const getStockStatus = (quantity, reorderLevel) => {
        if (quantity === 0) return { status: 'out_of_stock', label: 'Out of Stock', class: 'bg-red-100 text-red-800 border-red-200' };
        if (quantity <= reorderLevel) return { status: 'low_stock', label: 'Low Stock', class: 'bg-yellow-100 text-yellow-800 border-yellow-200' };
        return { status: 'in_stock', label: 'In Stock', class: 'bg-green-100 text-green-800 border-green-200' };
    };

    const stockStatus = getStockStatus(mockItem.quantity, mockItem.reorder_level);

    return (
        <CaterProLayout>
            <Head title={`${mockItem.name} - Inventory`} />

            <div className="max-w-4xl mx-auto space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('inventory.index')}>
                            <Button variant="ghost">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Inventory
                            </Button>
                        </Link>
                        <Separator orientation="vertical" className="h-6" />
                        <div>
                            <h1 className="text-3xl font-bold text-foreground flex items-center">
                                <Package className="h-6 w-6 mr-3 text-primary" />
                                {mockItem.name}
                            </h1>
                            <p className="text-muted-foreground mt-2">SKU: {mockItem.sku}</p>
                        </div>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('inventory.edit', mockItem.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Item
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>
                {/* Item Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center">
                                <Package className="h-5 w-5 mr-2 text-primary" />
                                Current Stock
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold">{mockItem.quantity}</div>
                            <div className="text-sm text-muted-foreground">{mockItem.unit.name}</div>
                            <Badge className={stockStatus.class + " mt-2"}>
                                {stockStatus.label}
                            </Badge>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center">
                                <DollarSign className="h-5 w-5 mr-2 text-primary" />
                                Cost Price
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold">${mockItem.cost_price}</div>
                            <div className="text-sm text-muted-foreground">per {mockItem.unit.abbreviation}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center">
                                <DollarSign className="h-5 w-5 mr-2 text-primary" />
                                Selling Price
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold">${mockItem.selling_price || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">per {mockItem.unit.abbreviation}</div>
                            {mockItem.selling_price && (
                                <div className="text-sm text-green-600 mt-1">
                                    Margin: ${(mockItem.selling_price - mockItem.cost_price).toFixed(2)}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Item Details */}
                <Card>
                    <CardHeader>
                        <CardTitle>Item Details</CardTitle>
                        <CardDescription>Complete information about this inventory item</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <div>
                                    <Label className="text-sm font-medium flex items-center mb-2">
                                        <Hash className="h-4 w-4 mr-1" />
                                        SKU
                                    </Label>
                                    <div className="text-lg">{mockItem.sku}</div>
                                </div>
                                
                                <div>
                                    <Label className="text-sm font-medium flex items-center mb-2">
                                        <Building className="h-4 w-4 mr-1" />
                                        Category
                                    </Label>
                                    <div className="text-lg">{mockItem.category.name}</div>
                                </div>

                                <div>
                                    <Label className="text-sm font-medium flex items-center mb-2">
                                        <User className="h-4 w-4 mr-1" />
                                        Supplier
                                    </Label>
                                    <div className="text-lg">{mockItem.supplier.name}</div>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div>
                                    <Label className="text-sm font-medium mb-2 block">Unit of Measurement</Label>
                                    <div className="text-lg">{mockItem.unit.name} ({mockItem.unit.abbreviation})</div>
                                </div>

                                <div>
                                    <Label className="text-sm font-medium mb-2 block">Reorder Level</Label>
                                    <div className="text-lg">{mockItem.reorder_level} {mockItem.unit.abbreviation}</div>
                                </div>

                                <div>
                                    <Label className="text-sm font-medium flex items-center mb-2">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </Label>
                                    <div className="text-lg">{new Date(mockItem.updated_at).toLocaleDateString()}</div>
                                </div>
                            </div>
                        </div>

                        {mockItem.description && (
                            <>
                                <Separator />
                                <div>
                                    <Label className="text-sm font-medium mb-2 block">Description</Label>
                                    <div className="text-muted-foreground">{mockItem.description}</div>
                                </div>
                            </>
                        )}
                    </CardContent>
                </Card>

                {/* Stock Alert */}
                {stockStatus.status === 'low_stock' && (
                    <Card className="border-yellow-200 bg-yellow-50">
                        <CardHeader>
                            <CardTitle className="text-yellow-800">Low Stock Alert</CardTitle>
                            <CardDescription className="text-yellow-700">
                                This item is running low and may need restocking soon.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="text-sm text-yellow-700">
                                        Current: {mockItem.quantity} {mockItem.unit.abbreviation} | 
                                        Reorder Level: {mockItem.reorder_level} {mockItem.unit.abbreviation}
                                    </div>
                                </div>
                                <Button variant="outline" className="border-yellow-300 text-yellow-800 hover:bg-yellow-100">
                                    Restock Now
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </CaterProLayout>
    );
}

// Helper component for labels
function Label({ children, className = "", ...props }) {
    return (
        <label className={`text-sm font-medium ${className}`} {...props}>
            {children}
        </label>
    );
}
