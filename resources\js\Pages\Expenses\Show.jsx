import CaterProLayout from '@/Layouts/CaterProLayout';
import { Head, Link } from '@inertiajs/react';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import { DollarSign, ArrowLeft, Edit, Trash2, Receipt, Calendar, Tag } from 'lucide-react';

export default function Show({ auth, expense }) {
    // Mock data if not provided
    const expenseData = expense || {
        id: 1,
        title: "Food Supplies - Wholesale Market",
        description: "Weekly food supplies purchase for catering events including fresh vegetables, meat, and dairy products.",
        amount: 1250.00,
        category: "Food & Ingredients",
        expense_date: "2024-01-20",
        receipt_number: "RCP-001-2024",
        created_at: "2024-01-20T10:30:00Z",
        updated_at: "2024-01-20T14:45:00Z"
    };

    const getCategoryColor = (category) => {
        const colors = {
            'Food & Ingredients': 'bg-green-100 text-green-800',
            'Equipment': 'bg-blue-100 text-blue-800',
            'Transportation': 'bg-yellow-100 text-yellow-800',
            'Marketing': 'bg-purple-100 text-purple-800',
            'Utilities': 'bg-orange-100 text-orange-800',
            'Other': 'bg-gray-100 text-gray-800'
        };
        return colors[category] || 'bg-gray-100 text-gray-800';
    };

    return (
        <CaterProLayout>
            <Head title={`Expense: ${expenseData.title}`} />

            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-foreground flex items-center">
                            <DollarSign className="h-6 w-6 mr-3 text-primary" />
                            {expenseData.title}
                        </h1>
                        <p className="text-muted-foreground mt-2">Expense details and information</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" asChild>
                            <Link href={route('expenses.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Expenses
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={route('expenses.edit', expenseData.id)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                            </Link>
                        </Button>
                        <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Information */}
                    <div className="lg:col-span-2 space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Expense Information</CardTitle>
                                <CardDescription>Details about this expense</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2">Expense Title</h3>
                                        <p className="text-lg font-semibold">{expenseData.title}</p>
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center">
                                            <Tag className="h-4 w-4 mr-1" />
                                            Category
                                        </h3>
                                        <Badge className={getCategoryColor(expenseData.category)}>
                                            {expenseData.category}
                                        </Badge>
                                    </div>
                                </div>

                                <Separator />

                                <div>
                                    <h3 className="font-medium text-sm text-muted-foreground mb-2">Description</h3>
                                    <p className="text-foreground leading-relaxed">
                                        {expenseData.description || 'No description provided'}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Expense Details</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <DollarSign className="h-4 w-4 mr-1" />
                                        Amount
                                    </span>
                                    <span className="text-lg font-semibold">
                                        ${expenseData.amount.toFixed(2)}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Date
                                    </span>
                                    <span className="text-sm">
                                        {new Date(expenseData.expense_date).toLocaleDateString()}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium flex items-center">
                                        <Receipt className="h-4 w-4 mr-1" />
                                        Receipt #
                                    </span>
                                    <span className="text-sm">
                                        {expenseData.receipt_number || 'N/A'}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Record Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Created
                                    </span>
                                    <p className="text-sm">{new Date(expenseData.created_at).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-muted-foreground flex items-center mb-1">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        Last Updated
                                    </span>
                                    <p className="text-sm">{new Date(expenseData.updated_at).toLocaleDateString()}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </CaterProLayout>
    );
}
